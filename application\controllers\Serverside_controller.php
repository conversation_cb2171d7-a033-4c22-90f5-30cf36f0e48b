<?php 
defined('BASEPATH') OR exit('No direct script access allowed');
class Serverside_controller extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
	}
	public function musterigoruntule_serverside()
	{
	// DataTables tarafından gönderilen parametreleri alın
		$draw = $this->input->post('draw');
		$start = $this->input->post('start');
		$length = $this->input->post('length');
		$order = $this->input->post('order');
		$search = $this->input->post('search');
		$sayi = 0;
		// Sayfalama
		$musteriler = musteri::query("SELECT * FROM musteri WHERE name <> 'barkod' AND kart_id <> '831475652' AND kart_id <> '35214629' LIMIT $start , $length");
    	// Sıralama
		if (!empty($order)) {
			$sort_col = $order[0]['column'];
			$sort_dir = $order[0]['dir'];
			$columns = array('id', 'kart_id', 'name', 'telefon', 'bakiye');
			$musteriler = musteri::query("SELECT * FROM musteri WHERE name <> 'barkod' AND kart_id <> '831475652' AND kart_id <> '35214629' ORDER BY $columns[$sort_col] $sort_dir LIMIT $start , $length");
		}

    	// Arama
		if (!empty($search['value'])) {
			$musteriler = musteri::query("SELECT * FROM musteri WHERE (name <> 'barkod') AND (kart_id <> '831475652') AND (kart_id <> '35214629') AND  (name LIKE '%$search[value]%' OR kart_id LIKE '%$search[value]%' OR plaka LIKE '%$search[value]%' OR telefon LIKE '%$search[value]%' OR bakiye LIKE '%$search[value]%') LIMIT $start , $length");
			$sayi = musteri::query("SELECT COUNT(id) AS 's' FROM musteri WHERE (name <> 'barkod') AND (kart_id <> '831475652') AND (kart_id <> '35214629') AND (name LIKE '%$search[value]%' OR kart_id LIKE '%$search[value]%' OR plaka LIKE '%$search[value]%' OR telefon LIKE '%$search[value]%' OR bakiye LIKE '%$search[value]%') LIMIT $start , $length");
		}
    	// Toplam kayıt sayısı
		if ($sayi == 0) 
		{
			$total = musteri::count(['name<>'=>'barkod']);
		}else
		{
			$total = $sayi[0]->s;
		}
    	// Verileri DataTables'e uygun biçimde JSON olarak işleyin
		$data = array();
		foreach ($musteriler as $musteri) {
			$row = array();
			$row[] = $musteri->id;
			$row[] = $musteri->kart_id;
			$row[] = $musteri->name;
			$row[] = $musteri->plaka;
			$row[] = $musteri->telefon;
			$row[] = $musteri->bakiye." ₺";
			$row[] = '<a href="'.base_url('rapor/musteriduzenle/'.$musteri->id.'').'" class="btn btn btn-success"><i class="fa fa-edit"></i> Düzenle</a>';
			$data[] = $row;
		}

    	// Yanıt olarak JSON verisi gönderin
		$output = array(
			'draw' => $draw,
			'recordsTotal' => $total,
			'recordsFiltered' => $total,
			'data' => $data,
		);
		echo json_encode($output);
	}
	public function barkodgoruntule_serverside()
	{
		// DataTables tarafından gönderilen parametreleri alın
		$draw = $this->input->post('draw');
		$start = $this->input->post('start');
		$length = $this->input->post('length');
		$order = $this->input->post('order');
		$search = $this->input->post('search');
		$sayi = 0;
		// Sayfalama
		$barkodlar = musteri::query("SELECT m.id AS 'id', m.kart_id AS 'kart_id',  m.bakiye AS 'bakiye', m.barkod_personel AS 'barkod_personel', b.personel_id AS 'personel_id', b.date_time AS 'date_time' FROM musteri m INNER JOIN bakiye_yukle b ON m.kart_id = b.kart_id WHERE m.name = 'barkod' LIMIT $start , $length");
    	// Sıralama
		if (!empty($order)) {
			$sort_col = $order[0]['column'];
			$sort_dir = $order[0]['dir'];
			$columns = array('id', 'kart_id', 'bakiye', 'barkod_personel','personel_id','date_time');
			$barkodlar = musteri::query("SELECT m.id AS 'id', m.kart_id AS 'kart_id',  m.bakiye AS 'bakiye', m.barkod_personel AS 'barkod_personel', b.personel_id AS 'personel_id', b.date_time AS 'date_time' FROM musteri m INNER JOIN bakiye_yukle b ON m.kart_id = b.kart_id WHERE m.name = 'barkod' ORDER BY $columns[$sort_col] $sort_dir LIMIT $start , $length");
		}
    	//Arama
		if (!empty($search['value'])) {
			$barkodlar = musteri::query("SELECT m.id AS 'id', m.kart_id AS 'kart_id',  m.bakiye AS 'bakiye', m.barkod_personel AS 'barkod_personel', b.personel_id AS 'personel_id', b.date_time AS 'date_time' FROM musteri m INNER JOIN bakiye_yukle b ON m.kart_id = b.kart_id WHERE (m.name = 'barkod') AND (m.id LIKE '%$search[value]%' OR m.kart_id LIKE '%$search[value]%' OR b.date_time LIKE '%$search[value]%' OR b.personel_id LIKE '%$search[value]%' OR m.barkod_personel LIKE '%$search[value]%') LIMIT $start , $length");
			$sayi = musteri::query("SELECT COUNT(m.id) AS 's', m.id AS 'id', m.kart_id AS 'kart_id',  m.bakiye AS 'bakiye', m.barkod_personel AS 'barkod_personel', b.personel_id AS 'personel_id', b.date_time AS 'date_time' FROM musteri m INNER JOIN bakiye_yukle b ON m.kart_id = b.kart_id WHERE (m.name = 'barkod') AND (m.id LIKE '%$search[value]%' OR m.kart_id LIKE '%$search[value]%' OR b.date_time LIKE '%$search[value]%' OR b.personel_id LIKE '%$search[value]%' OR m.barkod_personel LIKE '%$search[value]%') LIMIT $start , $length");
		}
		// Toplam kayıt sayısı
		if ($sayi == 0) 
		{
			$asdfg = musteri::query("SELECT COUNT(m.id) AS 'mm', m.kart_id AS 'kart_id',  m.bakiye AS 'bakiye', m.barkod_personel AS 'barkod_personel', b.personel_id AS 'personel_id', b.date_time AS 'date_time' FROM musteri m INNER JOIN bakiye_yukle b ON m.kart_id = b.kart_id WHERE m.name = 'barkod'");
			$total = $asdfg[0]->mm;
		}else
		{
			$total = $sayi[0]->s;
		}
    	// Verileri DataTables'e uygun biçimde JSON olarak işleyin
		$data = array();
		foreach ($barkodlar as $barkod) {
			$row = array();
			$row[] = $barkod->id;
			$row[] = $barkod->kart_id;
			$row[] = $barkod->bakiye." ₺";
			$row[] = $barkod->barkod_personel;
			$row[] = $barkod->personel_id;
			$row[] = date("d.m.Y || H:i", strtotime($barkod->date_time));
			$row[] = '<a href="'.base_url('admin/barkodyazdir/'.$barkod->kart_id.'').'" class="btn btn btn-success"><i class="fa fa-edit"></i> Tekrar Yazdır</a>';
			$data[] = $row;
		}

    	// Yanıt olarak JSON verisi gönderin
		$output = array(
			'draw' => $draw,
			'recordsTotal' => $total,
			'recordsFiltered' => $total,
			'data' => $data,
		);
		echo json_encode($output);
	}
	public function loglar_serverside()
	{
	// DataTables tarafından gönderilen parametreleri alın
		$draw = $this->input->post('draw');
		$start = $this->input->post('start');
		$length = $this->input->post('length');
		$order = $this->input->post('order');
		$search = $this->input->post('search');
		$sayi = 0;
		// Sayfalama
		$musteriler = loglar::query("SELECT * FROM loglar LIMIT $start , $length");
    	// Sıralama
		if (!empty($order)) {
			$sort_col = $order[0]['column'];
			$sort_dir = $order[0]['dir'];
			$columns = array('id', 'kart_id', 'calistimi', 'p', 'sure','eski_bakiye','yeni_bakiye','date','okuyucu_numarasi');
			$musteriler = loglar::query("SELECT * FROM loglar ORDER BY $columns[$sort_col] $sort_dir LIMIT $start , $length");
		}

    	// Arama
		if (!empty($search['value'])) {
			$musteriler = loglar::query("SELECT * FROM loglar JOIN musteri ON loglar.kart_id = musteri.kart_id WHERE loglar.id LIKE '%$search[value]%' OR loglar.kart_id LIKE '%$search[value]%' OR musteri.name LIKE '%$search[value]%' OR loglar.calistimi LIKE '%$search[value]%' OR loglar.sure LIKE '%$search[value]%' OR loglar.date LIKE '%$search[value]%' OR loglar.okuyucu_numarasi LIKE '%$search[value]%' LIMIT $start , $length");
			$sayi = loglar::query("SELECT COUNT(loglar.id) AS 's' FROM loglar JOIN musteri ON loglar.kart_id = musteri.kart_id WHERE loglar.id LIKE '%$search[value]%' OR loglar.kart_id LIKE '%$search[value]%' OR musteri.name LIKE '%$search[value]%' OR loglar.calistimi LIKE '%$search[value]%' OR loglar.sure LIKE '%$search[value]%' OR loglar.date LIKE '%$search[value]%' OR loglar.okuyucu_numarasi LIKE '%$search[value]%' LIMIT $start , $length");
		}
    	// Toplam kayıt sayısı
		if ($sayi == 0) 
		{
			$total = loglar::count();
		}else
		{
			$total = $sayi[0]->s;
		}
    	// Verileri DataTables'e uygun biçimde JSON olarak işleyin
		$data = array();
		foreach ($musteriler as $musteri) {
			if ($musteri->kart_id != '831475652' AND $musteri->kart_id != '35214629') {
			$customerid = $musteri->kart_id;
			$customer = musteri::query("SELECT * from musteri WHERE kart_id = '$customerid'");
			if ($musteri->calistimi == 1) 
			{
				$calistimi = "Aktif Çalışıyor";
			}else
			{
				$calistimi = "İşlem Tamamlandı";
			}
			if ($musteri->P == 1) 
			{
				$p = "Yıkama";
			}elseif($musteri->P == 2)
			{
				$p = "Köpük";
			}else
			{
				$p = "Süpürge";
			}
			$row = array();
			$row[] = $musteri->id;
			$row[] = $musteri->kart_id;
			$row[] = $customer[0]->name;
			$row[] = $calistimi;
			$row[] = $p;
			$row[] = $musteri->sure;
			$row[] = $musteri->eski_bakiye;
			$row[] = $musteri->yeni_bakiye;
			$row[] = $musteri->date;
			$row[] = $musteri->okuyucu_numarasi;
			$data[] = $row;
		}
		}

    	// Yanıt olarak JSON verisi gönderin
		$output = array(
			'draw' => $draw,
			'recordsTotal' => $total,
			'recordsFiltered' => $total,
			'data' => $data,
		);
		echo json_encode($output);

	}
}