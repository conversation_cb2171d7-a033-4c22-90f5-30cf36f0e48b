<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> sayfa başlangıcı -->
<section class="content">
  <div class="row">
    <div class="col-md-3"></div>
    <div class="col-md-6">
      <form method="post" action="<?php echo base_url('admin/kartiadeprocess'); ?>">
        <div class="card card-primary">
          <div class="card-body">
            <div class="form-group">
              <label for="inputName">İşlem Yapan Personel</label>
              <input type="text" class="form-control" required readonly name="personel" id="personel" value="<?php echo $this->session->admininfo->name; ?>">
            </div>
            <div class="form-group">
              <label for="inputName">Müşteri Adı ve Soyadı</label>  
              <select class="form-control select2bs4" name="musteri" required onchange="FetchState(this.value)">
                <option selected disabled>Seçiniz..</option>
                <?php foreach ($musteriler as $musteri) { ?>
                <option value="<?php echo $musteri->kart_id ?>"><?php echo $musteri->name; ?></option>
              <?php } ?>
              </select>
            </div>
            <div class="form-group">
              <label for="inputName">Mevcut Bakiyesi</label>  
              <input type="text" readonly name="musteri_bakiyesi" id="iadebakiye" value="" required class="form-control">
            </div>
            <div class="form-group">
              <label for="inputName">Kart Depozito Ücreti</label>  
              <input type="text" readonly name="depozito" id="iadedepozito" required class="form-control">
            </div>
          </div>
          <!-- /.card-body --> 
        </div>
        <!-- /.card -->
      </div>
    </div>
    <div class="row">
      <div class="col-md-3"></div>
      <div class="col-6">
        <a href="<?php echo base_url('admin/iptal'); ?>" class="btn btn-secondary">Vazgeç</a>
        <input type="submit" value="Kart İadesini Gerçekleştir" class="btn btn-success float-right">
      </div>
    </form>
  </div>
</section>
<!-- /.content -->
<script type="text/javascript" src="<?php echo base_url('js/custom3.js') ?>"> </script>
<?php $this->load->view('admin/include/footer'); ?>