<?php 
defined('BASEPATH') OR exit('No direct script access allowed');
class Rapor extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
	}
	
	public function dongubaslangic()
	{
		if (isPost()) {
			$data['saat'] = postvalue('dongubaslangicsaat');
			$id = 1;
			dongubaslangic::update($id,$data);

		}
		$id = 1;
		$data['saat']=dongubaslangic::find($id);
		$data['head']="Mesai Döngüsü Ayarlama Ekranı";
		if ($this->session->admininfo->mesaisaati_yetki=='1') 
		{
			$this->load->view('admin/pages/dongubaslangic',$data);
		}
		else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Mesai Döngü Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}
	}
	public function sorgula()
	{
		$data['head']="Mesai Döngüsü Ayarlama Ekranı";
		$this->load->view('admin/pages/sorgula',$data);
	}
	public function severus_snape()
	{
		//	PROMOSYON KARTLARINI BURAYA TANIMLAYABİLİRSİNİZ.
		$promosyonkartlari = "831475652,35214629";
		//	PROMOSYON KARTLARINI BURAYA TANIMLAYABİLİRSİNİZ.
		$snape=postvalue('tarih');
		$raporlar_denetim = bakiye::query("SELECT * FROM bakiye_yukle WHERE date_ = '$snape' ");
		$severus_snapee=$raporlar_denetim[0]->mevcut_dongu_baslangic; //bravest man i have ever seen    
		if (!$severus_snapee) {
			flash('danger','window-close','UYGUN TARİH SEÇİLMEDİ!!!','Lütfen raporlamak için uygun bir tarih giriniz');
			redirect('rapor/sorgula');
		}else{
			$combinedDT = date('Y-m-d H:i:s', strtotime("$snape $severus_snapee"));
			$start = new DateTimeImmutable($combinedDT);
			$datetime = $start->modify('+1 day');
			$date_time=$datetime->format('Y-m-d H:i:s');
			$iade = bakiye::query("SELECT DISTINCT kart_id FROM bakiye_yukle WHERE date_time BETWEEN '$combinedDT' AND '$date_time' ");
			$toplam = 0;
			foreach ($iade as $iade) {
				$iade_deneme=kiade::query("SELECT * FROM iade WHERE kart_id = '$iade->kart_id' ");
				$toplam = $toplam + (int) $iade_deneme[0]->depozito;
			}
			$data['depozito_toplam'] = $toplam;
			$gorevalmiskisiler = bakiye::query("SELECT DISTINCT personel_id FROM bakiye_yukle WHERE date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			$counter = 0;
			foreach ($gorevalmiskisiler as $gorev) {
				$counter += 1;
			}
			if ($counter == 1) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$data['raporlar1'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor1'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor11'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor1'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			}elseif ($counter == 2) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$data['raporlar1'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor1'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor11'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor1'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND  islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor2'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor22'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor2'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			}elseif ($counter == 3) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$data['raporlar1'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor1'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor11'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor1'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor2'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor22'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor2'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor3'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor33'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor3'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			}elseif ($counter == 4) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$gorevalmiskisi4 = $gorevalmiskisiler[3]->personel_id;
				$data['raporlar1'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor1'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor11'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor1'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor2'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor22'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor2'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor3'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor33'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor3'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor4'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor44'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor4'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			}elseif ($counter == 5) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$gorevalmiskisi4 = $gorevalmiskisiler[3]->personel_id;
				$gorevalmiskisi5 = $gorevalmiskisiler[4]->personel_id;
				$data['raporlar1'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor1'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor11'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor1'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor2'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor22'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor2'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor3'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor33'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor3'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor4'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor44'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor4'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar5'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor5'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor55'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor5'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar5bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar5promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			}elseif ($counter == 6) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$gorevalmiskisi4 = $gorevalmiskisiler[3]->personel_id;
				$gorevalmiskisi5 = $gorevalmiskisiler[4]->personel_id;
				$gorevalmiskisi6 = $gorevalmiskisiler[5]->personel_id;
				$data['raporlar1'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor1'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor11'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor1'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar1promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor2'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor22'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor2'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar2promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor3'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor33'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor3'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar3promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor4'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor44'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor4'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar4promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar5'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor5'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor55'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor5'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar5bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar5promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar6'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor6'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['iaderapor66'] = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['depozitorapor6'] = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar6bakiye'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi6' AND kart_id NOT IN ($promosyonkartlari) AND islemtipi = 1 AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$data['raporlar6promosyon'] = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi6' AND   islemtipi = 2  AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
			}
			
			$data['counter'] = $counter;
			$data['head'] = "Vardiya Bazlı Raporlar";
			$this->load->view('admin/pages/vardiyabazliraporlar',$data);

		}
	}
	public function musteriduzenle($id)
	{

		$data['musteri']=musteri::find($id);
		$data['head']="Müşteri Düzenleme Sayfası";
		if ($this->session->admininfo->musteriduzenle_yetki=='1') 
		{
			$this->load->view('admin/pages/musteriduzenle',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Müşteri Düzenlemeye yetkiniz bulunmamaktadır.');
			back();
		}


	}
	public function musteriduzenlee()
	{
		if(isPost())
		{
			$musteriadi = postvalue('name');
			$seflimusteriadi = sef($musteriadi);
			$data=[

				'name' 			=> $seflimusteriadi,
				'plaka' 		=> postvalue('plaka'),
				'telefon' 		=> postvalue('telefon'),
				'bakiye' 		=> postvalue('bakiye')

			];
			$id = postvalue('id');
			if ($this->session->admininfo->musteriduzenle_yetki=='1') 
			{
				musteri::update($id,$data);
			}else
			{
				flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Müşteri Düzenlemeye yetkiniz bulunmamaktadır.');
				back();
			}

		}
		redirect('admin/musterigoruntule');


	}
	public function mailgonder()
	{
		$data['head']="mail gönder";
		$this->load->view('admin/pages/mailgonder',$data);
	}
	public function mailgonderfunction()
	{
		$this->load->library("email");
		$this->load->view('admin/pages/mail');
	}
	public function vardiyabazlimailfunction()
	{
		$this->load->library("email");
		$this->load->view('admin/pages/vardiyabazlimail');
	}


}
