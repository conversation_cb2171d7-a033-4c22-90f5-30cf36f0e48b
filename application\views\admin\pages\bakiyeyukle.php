<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> say<PERSON> başlangıcı -->
<section class="content">
  <div class="row">
    <div class="col-md-3"></div>
    <div class="col-md-6">
      <form method="post" id="form" action="<?php echo base_url('admin/bakiyeekle'); ?>">
        <div class="card card-primary">
          <div class="card-body">
            <div class="form-group">
              <label for="inputName">İşlem Yapan Personel</label>
              <input type="text" class="form-control" required readonly name="personel" id="personel" value="<?php echo $this->session->admininfo->name; ?>">
            </div>
            <div class="form-group">
              <label for="inputName"><PERSON><PERSON></label>
              <input type="text" class="form-control" required readonly name="musteri" id="musteri">
              <input type="hidden" class="form-control" required readonly name="id" id="id">
            </div>
            <div class="form-group">
              <label>Müşteri Adı</label>
              <input type="text" class="form-control" readonly name="musteriadi" id="musteriadi">
            </div>
            <div class="form-group">
              <label for="inputName">Depozito Ücreti</label>
              <input type="text" class="form-control" required readonly name="depozito" id="depozito">
            </div>
            <div class="form-group">
              <label for="inputName">Şuanki Bakiye</label>  
              <input type="text" name="mevcut_bakiye" id="bakiye" readonly required class="form-control">
            </div>
            <div class="form-group">
              <label for="inputName">Eklenecek Bakiye</label>  
              <input type="number" id="eklenecek" name="eklenecek_bakiye" class="form-control">
            </div>
            <div class="form-group">
              <label for="inputName">Uygulanacak Promosyon</label>
              <select class="form-control" name="promosyon">
                <option selected disabled value="0">Promosyon Yok</option>
              </select> 
            </div>
          </div>
          <!-- /.card-body --> 
        </div>
        <!-- /.card -->
      </div>
    </div>
    <div class="row">
      <div class="col-md-3"></div>
      <div class="col-md-6" style="display: flex; justify-content: space-between;">
        <div style="order: 1; ">
          <a id="buton" class="btn btn-secondary">İşlem İptali</a>
        </div>
        <div style="order: 2;">
          <a id="kiade" class="btn btn-danger">Kart İade</a>
        </div>
        <div style="order: 3;">
          <input type="submit" value="Kaydet" class="btn btn-success">
        </div>
      </div>
    </form>
  </div>
</section>

<!-- /.content -->
<script>
  $(document).ready(function(){
  $("#buton").on("click", function(){ // buton idli elemana tıklandığında
    var gonderilenform = $("#form").serialize(); // idsi gonderilenform olan formun içindeki tüm elemanları serileştirdi ve gonderilenform adlı değişken oluşturarak içine attı
    $.ajax({
      url:'<?php echo base_url('admin/bakiyeiptal') ?>', // serileştirilen değerleri ajax.php dosyasına
      type:'POST', // post metodu ile 
      data:gonderilenform, // yukarıda serileştirdiğimiz gonderilenform değişkeni 
      success:function(e){ // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
        $("div").html("").html(e); // div elemanını her gönderme işleminde boşalttı ve gelen verileri içine attı
      }
    });
    location.reload();
  });
});
</script>
<script type="text/javascript">
  $("#kiade").on("click", function(){
    var musteripost = $("#musteri").val();
    var personelpost = $("#personel").val();
    var depozitopost = $("#depozito").val();
    var musteribakiyepost = $("#bakiye").val();
   $.ajax({
      url:'<?php echo base_url('admin/kartiadeprocess2') ?>', // serileştirilen değerleri ajax.php dosyasına
      type:'POST', // post metodu ile 
      data:{ musteri: musteripost,personel: personelpost,musteri_bakiyesi: musteribakiyepost,depozito:depozitopost}, // yukarıda serileştirdiğimiz gonderilenform değişkeni 
      success:function(e){ // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
        location.reload();
      }
    });
});
</script>
<script type="text/javascript" src="<?php echo base_url('js/custom.js') ?>"> </script>
<script type="text/javascript" src="<?php echo base_url('js/custom5.js') ?>"> </script>
<?php $this->load->view('admin/include/footer'); ?>