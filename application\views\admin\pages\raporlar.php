<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<?php 
if ($yikamaharcanan[0]->yikama) 
{
  $yikama = $yikamaharcanan[0]->yikama;
}else
{
  $yikama = 0;
}
if ($kopukharcanan[0]->kopuk) 
{
  $kopuk = $kopukharcanan[0]->kopuk;
}else
{
  $kopuk = 0;
}
?>
<!--Bakiye Ekle sayfa başlangıcı -->
<section class="content">
  <div class="row">
    <div class="col-md-6">
      <div class="card card-primary">
        <div class="card-body">
          <canvas id="myChart" height="542"></canvas>
        </div>
        <!-- /.card-body --> 
      </div>
    </div>
    <div class="col-md-6">
      <div class="card card-primary">
        <div class="card-body">
          <table id="vad" class="table table-bordered table-striped" style="width:100%">
            <thead>
              <tr>
                <th>Yıkama Ücretleri Toplamı</th>
                <th>Köpük Ücretleri Toplamı</th>
                <th>Depozito Ücretleri Toplamı</th>
                <th>Bakiye Toplamı</th>
                <th>Depozito Dahil Bakiye Toplamı</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <p><?php if ($yikamaharcanan[0]->yikama) 
                  {
                    echo $yikamaharcanan[0]->yikama." ₺";
                  }else
                  {
                    echo "0 ₺";
                  }  ?></p>
                </td>
                <td>
                  <p><?php if ($kopukharcanan[0]->kopuk) {
                    echo $kopukharcanan[0]->kopuk." ₺";
                  }else
                  {
                    echo "0 ₺";
                  }
                ?></p>
              </td>
              <td>
                <p><?php if ($depozitoguncel[0]->depozitoguncel) 
                {
                  echo $depozitoguncel[0]->depozitoguncel." ₺";
                }else
                {
                  echo "0 ₺";
                } 
              ?></p>
            </td>
            <td>
              <p>
                <?php if ($depozitosuzgeneltoplam[0]->bakiye) 
                {
                  echo $depozitosuzgeneltoplam[0]->bakiye." ₺";
                }else
                {
                  echo "0 ₺";
                }
                ?>
              </p>
            </td>
            <td>
              <p>
                <?php if ($depozitodahilgeneltoplam[0]->bakiye) 
                {
                  echo $depozitodahilgeneltoplam[0]->bakiye." ₺";
                }else
                {
                  echo "0 ₺";
                }
                ?>
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
<script>
  const labels = [
    'Köpük',
    'Su',
    ];
  const data = {
    labels: labels,
    datasets: [{
      label: 'My First dataset',
      backgroundColor: [
        "#4b77a9",
        "#5f255f"
        ], 
      data: [<?php echo $kopuk; ?>, <?php echo $yikama; ?>],
    }]
  };
  const config = {
    type: 'pie',
    data,
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        datalabels: {
          formatter: (value, ctx) => {

            let sum = 0;
            let dataArr = ctx.chart.data.datasets[0].data;
            dataArr.map(data => {
              sum += data;
            });
            let percentage = (value*100 / sum).toFixed(2)+"%";
            return percentage;


          },
          color: '#fff',
        }
      }
    }
  };
</script>
<script>
  var myChart = new Chart(
    document.getElementById('myChart'),
    config
    );
  </script>
</section>
<!-- /.content -->
<?php $this->load->view('admin/include/footer'); ?>