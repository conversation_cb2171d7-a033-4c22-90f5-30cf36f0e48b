<?php 
//
//
//
//
//BU KODLAR BAKIYE YUKLE TABLOSUNU LOCALDEN ALIR VE BUNLARI ANA DATABASEYE INSERT OLARAK BASARK ANA DATABASEDEKI TABLO SILINMEDEN  UZERINE BASILIR
//
//
//
//
//
$dosyaYolu = 'C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_canliya.sql';

if (file_exists($dosyaYolu)) {
	if (unlink($dosyaYolu)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}

$source_host = 'localhost';
$source_user = 'root';
$source_pass = '';
$source_db = 'petrolsistemi';
$table = "bakiye_yukle"; 
$excludeColumn = "id";
$source_conn = new mysqli($source_host, $source_user, $source_pass, $source_db);

if ($source_conn->connect_error) {
	die("Bağlantı hatası: " . $source_conn->connect_error);
}
$query = "SELECT * FROM $table";
$result = $source_conn->query($query);
if ($result->num_rows > 0) {
	$file = fopen('C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_canliya.sql', 'w');

	while ($row = $result->fetch_assoc())
	{
		unset($row[$excludeColumn]);
		$values = array_map('addslashes', $row);
		$insert_sql = "INSERT INTO $table (" . implode(", ", array_keys($values)) . ") VALUES ('" . implode("', '", $values) . "');\n";fwrite($file, $insert_sql);
	}
	fclose($file);
}else{
	echo "Tablo boş veya bulunamadı.";
}
$targethost = "***********";
$targetusername = "u411458722_pekdemir";
$targetpassword = "J$?7cy+18Z6:x";
$targetdbname = "u411458722_pekdemir";

$target_conn = new mysqli($targethost, $targetusername, $targetpassword, $targetdbname);

if ($target_conn->connect_error) {
	die("Bağlantı hatası: " . $target_conn->connect_error);
}

$sqlFile = 'C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_canliya.sql';

$sql = file_get_contents($sqlFile);

$queries = explode(';', $sql);

foreach ($queries as $query) {
	$query = trim($query);
	if (!empty($query)) {
		$result = $target_conn->query($query);
		if (!$result) {
			echo "Hata: " . $target_conn->error;
		}
	}
}

$target_conn->close();
$source_conn->close();
$dosyaYolu = 'C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_canliya.sql';

if (file_exists($dosyaYolu)) {
	if (unlink($dosyaYolu)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
?>