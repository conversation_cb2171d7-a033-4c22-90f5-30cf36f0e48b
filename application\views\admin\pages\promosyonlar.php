<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<section class="content">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xl-12">
				<div class="box box-solid">
					<div class="box-body">
						<table id="vad" class="table table-bordered table-striped" style="width:100%">
							<thead>
								<tr>
									<th>Hediye İşleminin Uygulanacağı Yükleme Miktarı</th>
									<th>Hediye Edilecek Yıkama Sayısı</th>
									<th>Hediye Edilecek Köpük Sayısı</th>
									<th>Oluşturulma Tarihi</th>
									<th>İşlemler</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($promosyonlar as $promosyon) 
								{ ?>
									<tr>
										<td>
											<p><?php echo $promosyon->yukleme_miktari; ?></p>
										</td>
										<td>
											<p><?php echo $promosyon->hediye_yikama; ?></p>
										</td>
										<td>
											<p><?php echo $promosyon->hediye_kopuk; ?></p>
										</td>
										<td>
											<p><?php echo $promosyon->date; ?></p>
										</td>
										<td>
											<button type="button" style="width: 45%;" class="btn btn btn-success" data-toggle="modal" data-target="#modal-duzenle-<?php echo $promosyon->id; ?>"><i class="fa fa-edit"></i>Düzenle</button>
											<button type="button" class="btn btn-danger" title="Promosyon Sil" data-toggle="modal" data-target="#modal-danger-<?php echo $promosyon->id; ?>"><i class="fas fa-trash"></i></button>
											<div class="modal fade" id="modal-duzenle-<?php echo $promosyon->id; ?>">
												<div class="modal-dialog">
													<div class="modal-content">
														<div class="modal-header">

															<button type="button" class="close" data-dismiss="modal" aria-label="Close">
																<span aria-hidden="true">&times;</span>
															</button>
														</div>
														<div class="modal-body">
															<form method="post" action="<?php echo base_url('admin/promosyonduzenle/'.$promosyon->id.''); ?>">
																<div class="form-group">
																	<label>Hediye İşleminin Uygulanacağı Yükleme Miktarı</label>
																	<input class="form-control" value="<?php echo $promosyon->yukleme_miktari; ?>" type="text" name="yukleme">
																</div>
																<div class="form-group">
																	<label>Hediye Edilecek Yıkama Sayısı</label>
																	<input class="form-control" value="<?php echo $promosyon->hediye_yikama; ?>" type="text" name="yikama">
																</div>
																<div class="form-group">
																	<label>Hediye Edilecek Köpük Sayısı</label>
																	<input class="form-control" value="<?php echo $promosyon->hediye_kopuk; ?>" type="text" name="kopuk">
																</div>
															</div>
															<div class="modal-footer justify-content-between">
																<button type="button" class="btn btn-outline-light" data-dismiss="modal">Vazgeç</button>
																<input type="submit" value="Düzenle" class="btn btn-outline-light"></a></form>
															</div>
														</div>
													</div>
												</div>
												<div class="modal fade" id="modal-danger-<?php echo $promosyon->id; ?>">
													<div class="modal-dialog">
														<div class="modal-content">
															<div class="modal-header">
																<button type="button" class="close" data-dismiss="modal" aria-label="Close">
																	<span aria-hidden="true">&times;</span>
																</button>
															</div>
															<div class="modal-body">
																<p>Silmek istediğinizden emin misiniz?&hellip;</p>
															</div>
															<div class="modal-footer justify-content-between">
																<button type="button" class="btn btn-outline-danger" data-dismiss="modal">Hayır</button>
																<a href="<?php echo base_url('Admin/promosyonsil/'.$promosyon->id.''); ?>" class="button"><button type="button" class="btn btn-outline-danger">Evet</button></a>
															</div>
														</div>
													</div>
												</div>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>
	<?php $this->load->view('admin/include/footer'); ?>