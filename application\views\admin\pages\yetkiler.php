<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> sayfa başlangıcı -->

<section class="content">
	<div class="row">
		<div class="col-md-3"></div>
		<div class="col-md-6">
			<form method="post" action="<?php echo base_url('admin/yetkiguncelle/'.$kullanici->id.''); ?>">
				<div class="card card-primary">
					<div class="card-body">
						<div class="form-group">
							<label for="inputStatus"><PERSON>üş<PERSON><PERSON><PERSON></label>
							<select id="inputStatus" class="form-control" name="musteriekle_yetki">
								<option value="<?php echo $kullanici->musteriekle_yetki; ?>" selected hidden><?php if ($kullanici->musteriekle_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0"><PERSON><PERSON><PERSON><PERSON><PERSON></option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Müşterileri Görüntüle</label>
							<select id="inputStatus" class="form-control" name="musterigoruntule_yetki">
								<option value="<?php echo $kullanici->musterigoruntule_yetki; ?>" selected hidden><?php if ($kullanici->musterigoruntule_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Bakiye Yükle</label>
							<select id="inputStatus" class="form-control" name="bakiyeyukle_yetki">
								<option value="<?php echo $kullanici->bakiyeyukle_yetki; ?>" selected hidden><?php if ($kullanici->bakiyeyukle_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Barkod Oluştur</label>
							<select id="inputStatus" class="form-control" name="barkodolustur_yetki">
								<option value="<?php echo $kullanici->barkodolustur_yetki; ?>" selected hidden><?php if ($kullanici->barkodolustur_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Barkodları Görüntüle</label>
							<select id="inputStatus" class="form-control" name="barkodlarigoruntule_yetki">
								<option value="<?php echo $kullanici->barkodlarigoruntule_yetki; ?>" selected hidden><?php if ($kullanici->barkodlarigoruntule_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Logları Görüntüle</label>
							<select id="inputStatus" class="form-control" name="loglarigoruntule_yetki">
								<option value="<?php echo $kullanici->loglarigoruntule_yetki; ?>" selected hidden><?php if ($kullanici->loglarigoruntule_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Fiyatlar</label>
							<select id="inputStatus" class="form-control" name="fiyatlar_yetki">
								<option value="<?php echo $kullanici->fiyatlar_yetki; ?>" selected hidden><?php if ($kullanici->fiyatlar_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Süreler</label>
							<select id="inputStatus" class="form-control" name="sureler_yetki">
								<option value="<?php echo $kullanici->sureler_yetki; ?>" selected hidden><?php if ($kullanici->sureler_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Raporlar</label>
							<select id="inputStatus" class="form-control" name="raporlar_yetki">
								<option value="<?php echo $kullanici->raporlar_yetki; ?>" selected hidden><?php if ($kullanici->raporlar_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Kart İade</label>
							<select id="inputStatus" class="form-control" name="kartiade_yetki">
								<option value="<?php echo $kullanici->kartiade_yetki; ?>" selected hidden><?php if ($kullanici->kartiade_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Personel Ekle</label>
							<select id="inputStatus" class="form-control" name="personelekle_yetki">
								<option value="<?php echo $kullanici->personelekle_yetki; ?>" selected hidden><?php if ($kullanici->personelekle_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<div class="form-group">
							<label for="inputStatus">Personel Görüntüle</label>
							<select id="inputStatus" class="form-control" name="personelgoruntule_yetki">
								<option value="<?php echo $kullanici->personelgoruntule_yetki; ?>" selected hidden><?php if ($kullanici->personelgoruntule_yetki==0) {echo "YOK";}else{echo "VAR";} ?></option>
								<option value="0">Erişimi Kısıtla</option>
								<option value="1">Yetkilendir</option>
							</select>
						</div>
						<input type="submit" name="kaydet" value="Değişiklikleri Kaydet" class="btn btn-block btn-lg btn-success">
						<input type="submit" name="tumyetkileriver" value="Tüm Yetkileri Ver" class="btn btn-block btn-lg btn-success">
						<input type="submit" name="tumyetkilerial" value="Tüm Yetkileri Al" class="btn btn-block btn-lg btn-success">

					</div>
					<!-- /.card-body --> 
				</div>
				<!-- /.card -->
			</div>
		</div>
		<div class="row">
			<div class="col-md-3"></div>
		</form>
	</div>
</section>
<!-- /.content -->
<script type="text/javascript" src="<?php echo base_url('js/custom2.js') ?>"> </script>
<?php $this->load->view('admin/include/footer'); ?>
