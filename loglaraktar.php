<?php  
$target_host = "***********";
$target_user = "u411458722_pekdemir";
$target_pass = "?7cy+18Z6:x";
$target_db = "u411458722_pekdemir";
$target_conn = mysqli_connect($target_host, $target_user, $target_pass, $target_db);
$source_host = "localhost";
$source_user = "root";
$source_pass = "";
$source_db = "petrolsistemi";
$source_conn = mysqli_connect($source_host, $source_user, $source_pass, $source_db);
$source_kalinanid = "SELECT * FROM aktarimda_son_id WHERE id = '1' ";
$source_kalinanid_calistir = mysqli_query($source_conn,$source_kalinanid);
if ($target_conn) {
	while ($row = mysqli_fetch_assoc($source_kalinanid_calistir)) {
		$loglar_son_id = $row["loglar_son_id"];
	}

	$source_sorgu = "SELECT * FROM loglar WHERE id > '$loglar_son_id' "; 
	$source_sorgu_calistir = mysqli_query($source_conn,$source_sorgu); 
	if ($source_sorgu_calistir) {
		while ($row = mysqli_fetch_assoc($source_sorgu_calistir)) {
			$kart_id = $row["kart_id"];
			$calistimi = $row["islemtipi"];
			$PP = $row["P"];
			$sure = $row["sure"];
			$eski_bakiye = $row["eski_bakiye"];
			$yeni_bakiye = $row["yeni_bakiye"];
			$okuyucu_numarasi = $row["okuyucu_numarasi"];
			$dakikasaniye = $row["dakikasaniye"];
			$date = $row["date"];
			$id = $row["id"];
			$insertquery = "INSERT INTO bakiye_yukle (kart_id, calistimi, P, sure, eski_bakiye, yeni_bakiye, okuyucu_numarasi,dakikasaniye,  date)
			VALUES ('$kart_id', '$calistimi', '$PP', '$sure', '$eski_bakiye', '$yeni_bakiye', '$okuyucu_numarasi', '$dakikasaniye', '$date' )";
			$target_result = mysqli_query($target_conn, $insertquery);
		}
		$updatequery = "UPDATE aktarimda_son_id SET loglar_son_id = '$id' WHERE id = '1' ";
		$idguncelle = mysqli_query($source_conn, $updatequery);
	}
}else{
	echo "ana db ile baglanti kurulamadi";
}

?>