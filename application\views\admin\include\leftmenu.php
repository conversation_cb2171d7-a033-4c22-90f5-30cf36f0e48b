
<!-- Main Sidebar Container -->
<aside class="main-sidebar sidebar-dark-primary elevation-4">
  <!-- Brand Logo -->
  <a class="brand-link">
    <img src="<?=base_url()?>favicon.ico" alt="Technolobal Logo" class="brand-image img-circle elevation-3" style="opacity: .8">
    <span class="brand-text font-weight-light">Technolobal Bilişim</span>
  </a>

  <!-- Sidebar -->
  <div class="sidebar">
    <!-- Sidebar user panel (optional) -->
    <div class="user-panel mt-3 pb-3 mb-3 d-flex">
      <div class="image">
        <img src="<?php echo base_url('assets/upload/profil/user.jpg'); ?>" class="img-circle elevation-2" alt="User Image">
      </div>
      <div class="info">
        <a href="<?php echo base_url(); ?>" class="d-block"><?php echo $this->session->admininfo->name; ?></a>
      </div>
    </div>


    <!-- Sidebar Menu -->
    <nav class="mt-2">
      <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
          <!-- Add icons to the links using the .nav-icon class
           with font-awesome or any other icon font library -->
           <li class="nav-item">
            <a href="<?php echo base_url('admin/panel'); ?>" class="nav-link <?php active('panel'); ?>">
              <i class="nav-icon fas fa-th"></i>
              <p>
                Dashboard
              </p>
            </a>
          </li>
          
          <?php if ($this->session->admininfo->musteriekle_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/musteriekleview'); ?>" class="nav-link <?php active('musteriekleview'); ?>">
                <i class="nav-icon fas fa-user-plus"></i>
                <p>
                  Müşterileri Ekle
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->musterigoruntule_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/musterigoruntule'); ?>" class="nav-link <?php active('musterigoruntule'); ?>">
                <i class="nav-icon fas fa-users"></i>
                <p>
                  Müşterileri Görüntüle
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->bakiyeyukle_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/bakiyeyukle'); ?>" class="nav-link <?php active('bakiyeyukle'); ?>">
                <i class="nav-icon fas fa-plus-square"></i>
                <p>
                  Bakiye Yükle
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->barkodolustur_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/barkodolustur'); ?>" class="nav-link <?php active('barkodolustur'); ?>">
                <i class="nav-icon fas fa-barcode"></i>
                <p>
                  Barkod Oluştur
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->barkodlarigoruntule_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/barkodgoruntule'); ?>" class="nav-link <?php active('barkodgoruntule'); ?>">
                <i class="nav-icon fas fa-qrcode"></i>
                <p>
                  Barkodları Görüntüle
                </p>
              </a>
            </li>
          <?php } ?>
          <li class="nav-item">
              <a href="<?php echo base_url('admin/barkodsorgu'); ?>" class="nav-link <?php active('barkodsorgu'); ?>">
                <i class="nav-icon fas fa-search"></i>
                <p>
                  Barkod Sorgula
                </p>
              </a>
            </li>
          <?php if ($this->session->admininfo->loglarigoruntule_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/loggoruntule'); ?>" class="nav-link <?php active('loggoruntule'); ?>">
                <i class="nav-icon fas fa-eye"></i>
                <p>
                  Logları Görüntüle
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->fiyatlar_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/fiyatlar'); ?>" class="nav-link <?php active('fiyatlar'); ?>">
                <i class="nav-icon fas fa-money-check-alt"></i>
                <p>
                  Fiyatlar
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->sureler_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/sureler'); ?>" class="nav-link <?php active('sureler'); ?>">
                <i class="nav-icon fas fa-hourglass-start"></i>
                <p>
                  Süreler
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->raporlar_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/raporlar'); ?>" class="nav-link <?php active('raporlar'); ?>">
                <i class="nav-icon fas fa-chart-bar"></i>
                <p>
                  Raporlar
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->kartiade_yetki== 1) {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/kartiade'); ?>" class="nav-link <?php active('kartiade'); ?>">
                <i class="nav-icon fas fa-credit-card"></i>
                <p>
                  Kart İade
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/personelekle'); ?>" class="nav-link <?php active('personelekle'); ?>">
                <i class="nav-icon fas fa-people-arrows"></i>
                <p>
                  Personel Ekle
                </p>
              </a>
            </li>
          <?php } ?>
          <?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
            <li class="nav-item">
              <a href="<?php echo base_url('admin/personelgoruntule'); ?>" class="nav-link <?php active('personelgoruntule'); ?>">
               <i class="nav-icon fas fa-users-cog"></i>
               <p>
                Personel Görüntüle
              </p>
            </a>
          </li>
        <?php } ?>
        <?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
          <li class="nav-item">
            <a href="<?php echo base_url('admin/yetkilerview'); ?>" class="nav-link <?php active('yetkilerview'); ?>">
             <i class="nav-icon fas fa-user-shield"></i>
             <p>
              Yetkileri Güncelle
            </p>
          </a>
        </li>
      <?php } ?>
      <?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
        <li class="nav-item">
          <a href="<?php echo base_url('admin/promosyonekle'); ?>" class="nav-link <?php active('promosyonekle'); ?>">
           <i class="nav-icon fas fa-gifts"></i>
           <p>
            Promosyon Ekle
          </p>
        </a>
      </li>
    <?php } ?>
    <?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
      <li class="nav-item">
        <a href="<?php echo base_url('admin/promosyonlar'); ?>" class="nav-link <?php active('promosyonlar'); ?>">
         <i class="nav-icon fas fa-gift"></i>
         <p>
          Promosyonları Görüntüle
        </p>
      </a>
    </li>
  <?php } ?>
  <?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
    <li class="nav-item">
      <a href="<?php echo base_url('Rapor/dongubaslangic'); ?>" class="nav-link <?php active('dongubaslangic'); ?>">
       <i class="nav-icon fas fa-user-clock"></i>
       <p>
        Mesai Döngü Başlangıç Saati
      </p>
    </a>
  </li>
<?php } ?>
<?php if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") {?>
  <li class="nav-item">
    <a href="<?php echo base_url('Rapor/sorgula'); ?>" class="nav-link <?php active('sorgula'); ?>">
     <i class="nav-icon fas fa-wave-square"></i>
     <p>
      Vardiya Bazlı Raporlar
    </p>
  </a>
</li>
<?php } ?>
<!--
<li class="nav-item">
  <a href="<?php echo base_url('Rapor/mailgonder'); ?>" class="nav-link <?php active('mailgonder'); ?>">
   <i class="nav-icon fas fa-wave-square"></i>
   <p>
    mail gonder
  </p>
</a>
</li>
-->
</ul>
</nav>
<!-- /.sidebar-menu -->
</div>
<!-- /.sidebar -->
</aside>
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
  <!-- Content Header (Page header) -->
  <div class="content-header">
    <div class="container-fluid">
      <div class="row mb-2">
        <div class="col-sm-6">
          <h1 class="m-0"><?php if(isset($head)); {echo $head;} ?></h1>
        </div><!-- /.col -->
        <div class="col-sm-6">
          <ol class="breadcrumb float-sm-right">
            <li class="breadcrumb-item"><a href="<?php echo base_url('admin/panel'); ?>">Anasayfa</a></li>
            <?php if ($head != "Dashboard") {?>
              <li class="breadcrumb-item active"><?php if(isset($head)); {echo $head;} ?></li>
            <?php } ?>
          </ol>
        </div><!-- /.col -->
      </div><!-- /.row -->
    </div><!-- /.container-fluid -->
  </div>
  <!-- /.content-header -->
  <?php flashread(); ?>