<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> say<PERSON> başlangıcı -->
<section class="content">
  <div class="row">
    <div class="col-md-3"></div>
    <div class="col-md-6">
      <form method="post" action="<?php echo base_url('admin/sureler'); ?>">
        <div class="card card-primary">
          <div class="card-body">
            <label>Dakika ve saniyeyi iki nokta üst üste ":" şeklinde ayırmanız gereklidir. <br> 5 Dakika için Örnek Format = 05:00 <br> 10 Dakika 30 Saniye için Örnek Format = 10:30 biçimindedir.</label>
            <br>
            <div class="form-group">
              <label for="inputName">Köpük Süresi</label>
              <input type="time" class="form-control" value="<?php echo $sureler->kopuksuresi; ?>" required name="kopuk">
            </div>
            <div class="form-group">
              <label for="inputName">Su Süresi</label>
              <input type="time" class="form-control" value="<?php echo $sureler->yikamasuresi; ?>" required name="su">
            </div>
			   <div class="form-group">
              <label for="inputName">Hava Süresi</label>
              <input type="time" class="form-control" value="<?php echo $sureler->hava; ?>" required name="hava">
            </div>
			   <div class="form-group">
              <label for="inputName">Süpürge Süresi</label>
              <input type="time" class="form-control" value="<?php echo $sureler->supurge; ?>" required name="supurge">
            </div>
          </div>
          <!-- /.card-body --> 
        </div>
        <!-- /.card -->
      </div>
    </div>
    <div class="row">
      <div class="col-md-3"></div>
      <div class="col-6">
        <input type="submit" value="Kaydet" class="btn btn-success float-right">
      </div>
    </form>
  </div>
</section>
<!-- /.content -->
<?php $this->load->view('admin/include/footer'); ?>