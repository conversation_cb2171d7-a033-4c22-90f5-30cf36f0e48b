/*! https://github.com/leeoniya/uPlot (v1.6.15) */
var uPlot=function(){"use strict";function n(n,e,r,t){var l;r=r||0;for(var a=2147483647>=(t=t||e.length-1);t-r>1;)n>e[l=a?r+t>>1:m((r+t)/2)]?r=l:t=l;return n-e[r]>e[t]-n?t:r}function e(n,e,r,t){for(var l=1==t?e:r;l>=e&&r>=l;l+=t)if(null!=n[l])return l;return-1}var r=[0,0];function t(n,e,t,l){return r[0]=0>t?R(n,-t):n,r[1]=0>l?R(e,-l):e,r}function l(n,e,r,l){var a,i,o,s=k(n),u=10==r?y:M;return n==e&&(-1==s?(n*=r,e/=r):(n/=r,e*=r)),l?(a=m(u(n)),i=x(u(e)),n=(o=t(b(r,a),b(r,i),a,i))[0],e=o[1]):(a=m(u(p(n))),i=m(u(p(e))),n=H(n,(o=t(b(r,a),b(r,i),a,i))[0]),e=F(e,o[1])),[n,e]}function a(n,e,r,t){var a=l(n,e,r,t);return 0==n&&(a[0]=0),0==e&&(a[1]=0),a}var i={mode:3,pad:.1},o={pad:0,soft:null,mode:0},s={min:o,max:o};function u(n,e,r,t){return V(r)?c(n,e,r):(o.pad=r,o.soft=t?0:null,o.mode=t?3:0,c(n,e,s))}function f(n,e){return null==n?e:n}function c(n,e,r){var t=r.min,l=r.max,a=f(t.pad,0),i=f(l.pad,0),o=f(t.hard,-z),s=f(l.hard,z),u=f(t.soft,z),c=f(l.soft,-z),v=f(t.mode,0),h=f(l.mode,0),d=e-n;1e-9>d&&(d=0,0!=n&&0!=e||(d=1e-9,2==v&&u!=z&&(a=0),2==h&&c!=-z&&(i=0)));var g=d||p(e)||1e3,x=y(g),k=b(10,m(x)),M=R(H(n-g*(0==d?0==n?.1:1:a),k/10),9),S=u>n||1!=v&&(3!=v||M>u)&&(2!=v||u>M)?z:u,D=_(o,S>M&&n>=S?S:w(S,M)),E=R(F(e+g*(0==d?0==e?.1:1:i),k/10),9),T=e>c||1!=h&&(3!=h||c>E)&&(2!=h||E>c)?-z:c,A=w(s,E>T&&T>=e?T:_(T,E));return D==A&&0==D&&(A=100),[D,A]}var v=new Intl.NumberFormat(navigator.language).format,h=Math,d=h.PI,p=h.abs,m=h.floor,g=h.round,x=h.ceil,w=h.min,_=h.max,b=h.pow,k=h.sign,y=h.log10,M=h.log2,S=(n,e)=>(void 0===e&&(e=1),h.asinh(n/e)),z=1/0;function D(n,e){return g(n/e)*e}function E(n,e,r){return w(_(n,e),r)}function T(n){return"function"==typeof n?n:()=>n}var A=n=>n,P=(n,e)=>e,W=()=>null,Y=()=>!0,C=(n,e)=>n==e;function F(n,e){return x(n/e)*e}function H(n,e){return m(n/e)*e}function R(n,e){return g(n*(e=Math.pow(10,e)))/e}var L=new Map;function I(n){return((""+n).split(".")[1]||"").length}function G(n,e,r,t){for(var l=[],a=t.map(I),i=e;r>i;i++)for(var o=p(i),s=R(b(n,i),o),u=0;t.length>u;u++){var f=t[u]*s,c=(0>f||0>i?o:0)+(a[u]>i?a[u]:0),v=R(f,c);l.push(v),L.set(v,c)}return l}var B={},N=[null,null],O=Array.isArray;function j(n){return"string"==typeof n}function V(n){var e=!1;if(null!=n){var r=n.constructor;e=null==r||r==Object}return e}function U(n){return null!=n&&"object"==typeof n}function J(n,e){var r;if(e=e||V,O(n))r=n.map((n=>J(n,e)));else if(e(n))for(var t in r={},n)r[t]=J(n[t],e);else r=n;return r}function q(n){for(var e=arguments,r=1;e.length>r;r++){var t=e[r];for(var l in t)V(n[l])?q(n[l],J(t[l])):n[l]=J(t[l])}return n}function Z(n,e,r){for(var t=0,l=void 0,a=-1;e.length>t;t++){var i=e[t];if(i>a){for(l=i-1;l>=0&&null==n[l];)n[l--]=null;for(l=i+1;r>l&&null==n[l];)n[a=l++]=null}}}var K,X,Q="undefined"==typeof queueMicrotask?n=>Promise.resolve().then(n):queueMicrotask,$="width",nn="height",en="top",rn="bottom",tn="left",ln="right",an="#000",on="#0000",sn="mousemove",un="mousedown",fn="mouseup",cn="mouseenter",vn="mouseleave",hn="dblclick",dn="change",pn="dppxchange",mn="u-off",gn="u-label",xn=document,wn=window;function _n(n,e){if(null!=e){var r=n.classList;!r.contains(e)&&r.add(e)}}function bn(n,e){var r=n.classList;r.contains(e)&&r.remove(e)}function kn(n,e,r){n.style[e]=r+"px"}function yn(n,e,r,t){var l=xn.createElement(n);return null!=e&&_n(l,e),null!=r&&r.insertBefore(l,t),l}function Mn(n,e){return yn("div",n,e)}var Sn=new WeakMap;function zn(n,e,r,t,l){var a="translate("+e+"px,"+r+"px)";a!=Sn.get(n)&&(n.style.transform=a,Sn.set(n,a),0>e||0>r||e>t||r>l?_n(n,mn):bn(n,mn))}var Dn=new WeakMap;function En(n,e,r){var t=e+r;t!=Dn.get(n)&&(Dn.set(n,t),n.style.background=e,n.style.borderColor=r)}var Tn={passive:!0},An=q({capture:!0},Tn);function Pn(n,e,r,t){e.addEventListener(n,r,t?An:Tn)}function Wn(n,e,r,t){e.removeEventListener(n,r,t?An:Tn)}!function n(){K=devicePixelRatio,X&&Wn(dn,X,n),X=matchMedia("screen and (min-resolution: "+(K-.001)+"dppx) and (max-resolution: "+(K+.001)+"dppx)"),Pn(dn,X,n),wn.dispatchEvent(new CustomEvent(pn))}();var Yn=["January","February","March","April","May","June","July","August","September","October","November","December"],Cn=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];function Fn(n){return n.slice(0,3)}var Hn=Cn.map(Fn),Rn=Yn.map(Fn),Ln={MMMM:Yn,MMM:Rn,WWWW:Cn,WWW:Hn};function In(n){return(10>n?"0":"")+n}var Gn={YYYY:n=>n.getFullYear(),YY:n=>(n.getFullYear()+"").slice(2),MMMM:(n,e)=>e.MMMM[n.getMonth()],MMM:(n,e)=>e.MMM[n.getMonth()],MM:n=>In(n.getMonth()+1),M:n=>n.getMonth()+1,DD:n=>In(n.getDate()),D:n=>n.getDate(),WWWW:(n,e)=>e.WWWW[n.getDay()],WWW:(n,e)=>e.WWW[n.getDay()],HH:n=>In(n.getHours()),H:n=>n.getHours(),h:n=>{var e=n.getHours();return 0==e?12:e>12?e-12:e},AA:n=>12>n.getHours()?"AM":"PM",aa:n=>12>n.getHours()?"am":"pm",a:n=>12>n.getHours()?"a":"p",mm:n=>In(n.getMinutes()),m:n=>n.getMinutes(),ss:n=>In(n.getSeconds()),s:n=>n.getSeconds(),fff:n=>function(n){return(10>n?"00":100>n?"0":"")+n}(n.getMilliseconds())};function Bn(n,e){e=e||Ln;for(var r,t=[],l=/\{([a-z]+)\}|[^{]+/gi;r=l.exec(n);)t.push("{"==r[0][0]?Gn[r[1]]:r[0]);return n=>{for(var r="",l=0;t.length>l;l++)r+="string"==typeof t[l]?t[l]:t[l](n,e);return r}}var Nn=(new Intl.DateTimeFormat).resolvedOptions().timeZone,On=n=>n%1==0,jn=[1,2,2.5,5],Vn=G(10,-16,0,jn),Un=G(10,0,16,jn),Jn=Un.filter(On),qn=Vn.concat(Un),Zn="{YYYY}",Kn="\n"+Zn,Xn="{M}/{D}",Qn="\n"+Xn,$n=Qn+"/{YY}",ne="{aa}",ee="{h}:{mm}"+ne,re="\n"+ee,te=":{ss}",le=null;function ae(n){var e=1e3*n,r=60*e,t=60*r,l=24*t,a=30*l,i=365*l;return[(1==n?G(10,0,3,jn).filter(On):G(10,-3,0,jn)).concat([e,5*e,10*e,15*e,30*e,r,5*r,10*r,15*r,30*r,t,2*t,3*t,4*t,6*t,8*t,12*t,l,2*l,3*l,4*l,5*l,6*l,7*l,8*l,9*l,10*l,15*l,a,2*a,3*a,4*a,6*a,i,2*i,5*i,10*i,25*i,50*i,100*i]),[[i,Zn,le,le,le,le,le,le,1],[28*l,"{MMM}",Kn,le,le,le,le,le,1],[l,Xn,Kn,le,le,le,le,le,1],[t,"{h}"+ne,$n,le,Qn,le,le,le,1],[r,ee,$n,le,Qn,le,le,le,1],[e,te,$n+" "+ee,le,Qn+" "+ee,le,re,le,1],[n,te+".{fff}",$n+" "+ee,le,Qn+" "+ee,le,re,le,1]],function(e){return(o,s,u,f,c,v)=>{var h=[],d=c>=i,p=c>=a&&i>c,x=e(u),w=R(x*n,3),_=me(x.getFullYear(),d?0:x.getMonth(),p||d?1:x.getDate()),b=R(_*n,3);if(p||d)for(var k=p?c/a:0,y=d?c/i:0,M=w==b?w:R(me(_.getFullYear()+y,_.getMonth()+k,1)*n,3),S=new Date(g(M/n)),z=S.getFullYear(),D=S.getMonth(),E=0;f>=M;E++){var T=me(z+y*E,D+k*E,1),A=T-e(R(T*n,3));(M=R((+T+A)*n,3))>f||h.push(M)}else{var P=l>c?c:l,W=b+(m(u)-m(w))+F(w-b,P);h.push(W);for(var Y=e(W),C=Y.getHours()+Y.getMinutes()/r+Y.getSeconds()/t,H=c/t,L=v/o.axes[s]._space;(W=R(W+c,1==n?0:3))<=f;)if(H>1){var I=m(R(C+H,6))%24,G=e(W).getHours()-I;G>1&&(G=-1),C=(C+H)%24,.7>R(((W-=G*t)-h[h.length-1])/c,3)*L||h.push(W)}else h.push(W)}return h}}]}var ie=ae(1),oe=ie[0],se=ie[1],ue=ie[2],fe=ae(.001),ce=fe[0],ve=fe[1],he=fe[2];function de(n,e){return n.map((n=>n.map(((r,t)=>0==t||8==t||null==r?r:e(1==t||0==n[8]?r:n[1]+r)))))}function pe(n,e){return(r,t,l,a,i)=>{var o,s,u,f,c,v,h=e.find((n=>i>=n[0]))||e[e.length-1];return t.map((e=>{var r=n(e),t=r.getFullYear(),l=r.getMonth(),a=r.getDate(),i=r.getHours(),d=r.getMinutes(),p=r.getSeconds(),m=t!=o&&h[2]||l!=s&&h[3]||a!=u&&h[4]||i!=f&&h[5]||d!=c&&h[6]||p!=v&&h[7]||h[1];return o=t,s=l,u=a,f=i,c=d,v=p,m(r)}))}}function me(n,e,r){return new Date(n,e,r)}function ge(n,e){return e(n)}function xe(n,e){return(r,t)=>e(n(t))}G(2,-53,53,[1]);var we={show:!0,live:!0,isolate:!1,markers:{show:!0,width:2,stroke:function(n,e){var r=n.series[e];return r.width?r.stroke(n,e):r.points.width?r.points.stroke(n,e):null},fill:function(n,e){return n.series[e].fill(n,e)},dash:"solid"},idx:null,idxs:null,values:[]},_e=[0,0];function be(n,e,r){return n=>{0==n.button&&r(n)}}function ke(n,e,r){return r}var ye={show:!0,x:!0,y:!0,lock:!1,move:function(n,e,r){return _e[0]=e,_e[1]=r,_e},points:{show:function(n,e){var r=n.cursor.points,t=Mn(),l=r.size(n,e);kn(t,$,l),kn(t,nn,l);var a=l/-2;kn(t,"marginLeft",a),kn(t,"marginTop",a);var i=r.width(n,e,l);return i&&kn(t,"borderWidth",i),t},size:function(n,e){return Be(n.series[e].points.width,1)},width:0,stroke:function(n,e){var r=n.series[e].points;return r._stroke||r._fill},fill:function(n,e){var r=n.series[e].points;return r._fill||r._stroke}},bind:{mousedown:be,mouseup:be,click:be,dblclick:be,mousemove:ke,mouseleave:ke,mouseenter:ke},drag:{setScale:!0,x:!0,y:!1,dist:0,uni:null,_x:!1,_y:!1},focus:{prox:-1},left:-10,top:-10,idx:null,dataIdx:function(n,e,r){return r},idxs:null},Me={show:!0,stroke:"rgba(0,0,0,0.07)",width:2,filter:P},Se=q({},Me,{size:10}),ze='12px system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',De="bold "+ze,Ee={show:!0,scale:"x",stroke:an,space:50,gap:5,size:50,labelGap:0,labelSize:30,labelFont:De,side:2,grid:Me,ticks:Se,font:ze,rotate:0},Te={show:!0,scale:"x",auto:!1,sorted:1,min:z,max:-z,idxs:[]};function Ae(n,e){return e.map((n=>null==n?"":v(n)))}function Pe(n,e,r,t,l,a,i){for(var o=[],s=L.get(l)||0,u=r=i?r:R(F(r,l),s);t>=u;u=R(u+l,s))o.push(Object.is(u,-0)?0:u);return o}function We(n,e,r,t,l){var a=[],i=n.scales[n.axes[e].scale].log,o=m((10==i?y:M)(r));l=b(i,o),0>o&&(l=R(l,-o));var s=r;do{a.push(s),l*i>(s=R(s+l,L.get(l)))||(l=s)}while(t>=s);return a}function Ye(n,e,r,t,l){var a=n.scales[n.axes[e].scale].asinh,i=t>a?We(n,e,_(a,r),t,l):[a],o=0>t||r>0?[]:[0];return(-a>r?We(n,e,_(a,-t),-r,l):[a]).reverse().map((n=>-n)).concat(o,i)}var Ce=/./,Fe=/[12357]/,He=/[125]/,Re=/1/;function Le(n,e,r){var t=n.axes[r],l=t.scale,a=n.scales[l];if(3==a.distr&&2==a.log)return e;var i=n.valToPos,o=t._space,s=i(10,l),u=i(9,l)-s<o?i(7,l)-s<o?i(5,l)-s<o?Re:He:Fe:Ce;return e.map((n=>4==a.distr&&0==n||u.test(n)?n:null))}function Ie(n,e){return null==e?"":v(e)}var Ge={show:!0,scale:"y",stroke:an,space:30,gap:5,size:50,labelGap:0,labelSize:30,labelFont:De,side:3,grid:Me,ticks:Se,font:ze,rotate:0};function Be(n,e){return R((3+2*(n||1))*e,3)}function Ne(n,e){var r=n.scales[n.series[e].scale],t=n.bands&&n.bands.some((n=>n.series[0]==e));return 3==r.distr||t?r.min:0}var Oe={scale:"y",auto:!0,sorted:0,show:!0,spanGaps:!1,gaps:(n,e,r,t,l)=>l,alpha:1,points:{show:function(n,e){var r=n.series[0],t=r.scale,l=r.idxs,a=n._data[0],i=n.valToPos(a[l[0]],t,!0),o=n.valToPos(a[l[1]],t,!0);return p(o-i)/(n.series[e].points.space*K)>=l[1]-l[0]},filter:null},values:null,min:z,max:-z,idxs:[],path:null,clip:null};function je(n,e,r){return r/10}var Ve={time:!0,auto:!0,distr:1,log:10,asinh:1,min:null,max:null,dir:1,ori:0},Ue=q({},Ve,{time:!1,ori:1}),Je={};function qe(n){var e=Je[n];return e||(e={key:n,plots:[],sub:function(n){e.plots.push(n)},unsub:function(n){e.plots=e.plots.filter((e=>e!=n))},pub:function(n,r,t,l,a,i,o){for(var s=0;e.plots.length>s;s++)e.plots[s]!=r&&e.plots[s].pub(n,r,t,l,a,i,o)}},null!=n&&(Je[n]=e)),e}function Ze(n,e,r){var t=n.series[e],l=n.scales,a=n.bbox,i=n._data[0],o=n._data[e],s=l[n.series[0].scale],u=l[t.scale],f=a.left,c=a.top,v=a.width,h=a.height,d=n.valToPosH,p=n.valToPosV;return 0==s.ori?r(t,i,o,s,u,d,p,f,c,v,h,nr,rr,lr,ir,sr):r(t,i,o,s,u,p,d,c,f,h,v,er,tr,ar,or,ur)}function Ke(n,e,r,t,l){return Ze(n,e,((n,e,a,i,o,s,u,f,c,v,h)=>{var d,p,m=0==i.ori?rr:tr;1==i.dir*(0==i.ori?1:-1)?(d=r,p=t):(d=t,p=r);var g=D(s(e[d],i,v,f),.5),x=D(u(a[d],o,h,c),.5),w=D(s(e[p],i,v,f),.5),_=D(u(o.max,o,h,c),.5),b=new Path2D(l);return m(b,w,_),m(b,g,_),m(b,g,x),b}))}function Xe(n,e,r,t,l,a){var i=null;if(n.length>0){i=new Path2D;for(var o=0==e?lr:ar,s=r,u=0;n.length>u;u++){var f=n[u];f[1]>f[0]&&(o(i,s,t,f[0]-s,t+a),s=f[1])}o(i,s,t,r+l-s,t+a)}return i}function Qe(n,e,r){var t=n[n.length-1];t&&t[0]==e?t[1]=r:n.push([e,r])}function $e(n){return 0==n?A:1==n?g:e=>D(e,n)}function nr(n,e,r){n.moveTo(e,r)}function er(n,e,r){n.moveTo(r,e)}function rr(n,e,r){n.lineTo(e,r)}function tr(n,e,r){n.lineTo(r,e)}function lr(n,e,r,t,l){n.rect(e,r,t,l)}function ar(n,e,r,t,l){n.rect(r,e,l,t)}function ir(n,e,r,t,l,a){n.arc(e,r,t,l,a)}function or(n,e,r,t,l,a){n.arc(r,e,t,l,a)}function sr(n,e,r,t,l,a,i){n.bezierCurveTo(e,r,t,l,a,i)}function ur(n,e,r,t,l,a,i){n.bezierCurveTo(r,e,l,t,i,a)}function fr(){return(n,e,r,t,l)=>Ze(n,e,((e,a,i,o,s,u,f,c,v,h,p)=>{var m,g,x=e.pxRound,w=e.points;0==o.ori?(m=nr,g=ir):(m=er,g=or);var _=R(w.width*K,3),b=(w.size-w.width)/2*K,k=R(2*b,3),y=new Path2D,M=new Path2D,S=n.bbox;lr(M,S.left-k,S.top-k,S.width+2*k,S.height+2*k);var z=n=>{if(null!=i[n]){var e=x(u(a[n],o,h,c)),r=x(f(i[n],s,p,v));m(y,e+b,r),g(y,e,r,b,0,2*d)}};if(l)l.forEach(z);else for(var D=r;t>=D;D++)z(D);return{stroke:_>0?y:null,fill:y,clip:M,flags:3}}))}function cr(n){return(e,r,t,l,a,i)=>{t!=l&&(a!=t&&i!=t&&n(e,r,t),a!=l&&i!=l&&n(e,r,l),n(e,r,i))}}var vr=cr(rr),hr=cr(tr);function dr(){return(n,r,t,l)=>Ze(n,r,((a,i,o,s,u,f,c,v,h,d,p)=>{var m,g,x=a.pxRound;0==s.ori?(m=rr,g=vr):(m=tr,g=hr);var b,k,y,M,S=s.dir*(0==s.ori?1:-1),E={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:1},T=E.stroke,A=z,P=-z,W=[],Y=x(f(i[1==S?t:l],s,d,v)),C=!1,F=!1,H=e(o,t,l,1*S),R=e(o,t,l,-1*S),L=D(f(i[H],s,d,v),.5),I=D(f(i[R],s,d,v),.5);L>v&&Qe(W,v,L);for(var G=1==S?t:l;G>=t&&l>=G;G+=S){var B=x(f(i[G],s,d,v));if(B==Y)null!=o[G]?(k=x(c(o[G],u,p,h)),A==z&&(m(T,B,k),b=k),A=w(k,A),P=_(k,P)):null===o[G]&&(C=F=!0);else{var N=!1;A!=z?(g(T,Y,A,P,b,k),y=M=Y):C&&(N=!0,C=!1),null!=o[G]?(m(T,B,k=x(c(o[G],u,p,h))),A=P=b=k,F&&B-Y>1&&(N=!0),F=!1):(A=z,P=-z,null===o[G]&&(C=!0,B-Y>1&&(N=!0))),N&&Qe(W,y,B),Y=B}}if(A!=z&&A!=P&&M!=Y&&g(T,Y,A,P,b,k),v+d>I&&Qe(W,I,v+d),null!=a.fill){var O=E.fill=new Path2D(T),j=x(c(a.fillTo(n,r,a.min,a.max),u,p,h));m(O,I,j),m(O,L,j)}return E.gaps=W=a.gaps(n,r,t,l,W),a.spanGaps||(E.clip=Xe(W,s.ori,v,h,d,p)),n.bands.length>0&&(E.band=Ke(n,r,t,l,T)),E}))}function pr(n,e,r,t,l){var a=n.length;if(2>a)return null;var i=new Path2D;if(r(i,n[0],e[0]),2==a)t(i,n[1],e[1]);else{for(var o=Array(a),s=Array(a-1),u=Array(a-1),f=Array(a-1),c=0;a-1>c;c++)u[c]=e[c+1]-e[c],f[c]=n[c+1]-n[c],s[c]=u[c]/f[c];o[0]=s[0];for(var v=1;a-1>v;v++)0===s[v]||0===s[v-1]||s[v-1]>0!=s[v]>0?o[v]=0:(o[v]=3*(f[v-1]+f[v])/((2*f[v]+f[v-1])/s[v-1]+(f[v]+2*f[v-1])/s[v]),isFinite(o[v])||(o[v]=0));o[a-1]=s[a-2];for(var h=0;a-1>h;h++)l(i,n[h]+f[h]/3,e[h]+o[h]*f[h]/3,n[h+1]-f[h]/3,e[h+1]-o[h+1]*f[h]/3,n[h+1],e[h+1])}return i}var mr=new Set;function gr(){mr.forEach((n=>{n.syncRect(!0)}))}Pn("resize",wn,gr),Pn("scroll",wn,gr,!0);var xr=dr(),wr=fr();function _r(n,e,r,t){return(t?[n[0],n[1]].concat(n.slice(2)):[n[0]].concat(n.slice(1))).map(((n,t)=>br(n,t,e,r)))}function br(n,e,r,t){return q({},0==e?r:t,n)}function kr(n,e,r){return null==e?N:[e,r]}var yr=kr;function Mr(n,e,r){return null==e?N:u(e,r,.1,!0)}function Sr(n,e,r,t){return null==e?N:l(e,r,n.scales[t].log,!1)}var zr=Sr;function Dr(n,e,r,t){return null==e?N:a(e,r,n.scales[t].log,!1)}var Er=Dr;function Tr(n){var e,r;return[n=n.replace(/(\d+)px/,((n,t)=>(e=g((r=+t)*K))+"px")),e,r]}function Ar(n){n.show&&[n.font,n.labelFont].forEach((n=>{var e=R(n[2]*K,1);n[0]=n[0].replace(/[0-9.]+px/,e+"px"),n[1]=e}))}function Pr(e,r,t){var o={};function s(n,e){return((3==e.distr?y(n>0?n:e.clamp(o,n,e.min,e.max,e.key)):4==e.distr?S(n,e.asinh):n)-e._min)/(e._max-e._min)}function c(n,e,r,t){var l=s(n,e);return t+r*(-1==e.dir?1-l:l)}function v(n,e,r,t){var l=s(n,e);return t+r*(-1==e.dir?l:1-l)}function k(n,e,r,t){return 0==e.ori?c(n,e,r,t):v(n,e,r,t)}o.valToPosH=c,o.valToPosV=v;var M=!1;o.status=0;var A=o.root=Mn("uplot");null!=e.id&&(A.id=e.id),_n(A,e.class),e.title&&(Mn("u-title",A).textContent=e.title);var H=yn("canvas"),I=o.ctx=H.getContext("2d"),G=Mn("u-wrap",A),Z=o.under=Mn("u-under",G);G.appendChild(H);var X=o.over=Mn("u-over",G),an=+f((e=J(e)).pxAlign,1),dn=$e(an);(e.plugins||[]).forEach((n=>{n.opts&&(e=n.opts(o,e)||e)}));var Sn=e.ms||.001,Dn=o.series=_r(e.series||[],Te,Oe,!1),Tn=o.axes=_r(e.axes||[],Ee,Ge,!0),An=o.scales={},Yn=o.bands=e.bands||[];Yn.forEach((n=>{n.fill=T(n.fill||null)}));var Cn=Dn[0].scale,Fn={axes:function(){Tn.forEach(((n,e)=>{if(n.show&&n._show){var r,t,l=n.side,a=l%2,i=n.stroke(o,e),s=0==l||3==l?-1:1;if(n.label){var u=g((n._lpos+n.labelGap*s)*K);I.save(),1==a?(r=t=0,I.translate(u,g(er+tr/2)),I.rotate((3==l?-d:d)/2)):(r=g(nr+rr/2),t=u),I.font=n.labelFont[0],I.fillStyle=i,I.textAlign="center",I.textBaseline=2==l?en:rn,I.fillText(n.label,r,t),I.restore()}var f=n._found,c=f[0],v=f[1];if(0!=v){var h=An[n.scale],p=0==a?rr:tr,m=0==a?nr:er,x=g(n.gap*K),w=n._splits,_=2==h.distr?w.map((n=>Ir[n])):w,b=2==h.distr?Ir[w[1]]-Ir[w[0]]:c,y=n.ticks,M=y.show?g(y.size*K):0,S=n._rotate*-d/180,z=dn(n._pos*K),D=z+(M+x)*s;t=0==a?D:0,r=1==a?D:0,I.font=n.font[0],I.fillStyle=i,I.textAlign=1==n.align?tn:2==n.align?ln:S>0?tn:0>S?ln:0==a?"center":3==l?ln:tn,I.textBaseline=S||1==a?"middle":2==l?en:rn;var E=1.5*n.font[1],T=w.map((n=>dn(k(n,h,p,m))));n._values.forEach(((n,e)=>{null!=n&&(0==a?r=T[e]:t=T[e],(""+n).split(/\n/gm).forEach(((n,e)=>{S?(I.save(),I.translate(r,t+e*E),I.rotate(S),I.fillText(n,0,0),I.restore()):I.fillText(n,r,t+e*E)})))})),y.show&&Zr(T,y.filter(o,_,e,v,b),a,l,z,M,R(y.width*K,3),y.stroke(o,e),y.dash,y.cap);var A=n.grid;A.show&&Zr(T,A.filter(o,_,e,v,b),a,0==a?2:1,0==a?er:nr,0==a?tr:rr,R(A.width*K,3),A.stroke(o,e),A.dash,A.cap)}}})),qt("drawAxes")},series:function(){Yr>0&&(Dn.forEach(((n,e)=>{if(e>0&&n.show&&null==n._paths){var t=function(n){for(var e=E(Hr-1,0,Yr-1),r=E(Rr+1,0,Yr-1);null==n[e]&&e>0;)e--;for(;null==n[r]&&Yr-1>r;)r++;return[e,r]}(r[e]);n._paths=n.paths(o,e,t[0],t[1])}})),Dn.forEach(((n,e)=>{if(e>0&&n.show){jr(e,!1),n._paths&&Vr(e,!1),jr(e,!0);var r=n.points.show(o,e,Hr,Rr),t=n.points.filter(o,e,r,n._paths?n._paths.gaps:null);(r||t)&&(n.points._paths=n.points.paths(o,e,Hr,Rr,t),Vr(e,!0)),qt("drawSeries",e)}})))}},Hn=(e.drawOrder||["axes","series"]).map((n=>Fn[n]));function Rn(n){var r=An[n];if(null==r){var t=(e.scales||B)[n]||B;if(null!=t.from)Rn(t.from),An[n]=q({},An[t.from],t);else{(r=An[n]=q({},n==Cn?Ve:Ue,t)).key=n;var l=r.time,a=r.range,o=O(a);if(n!=Cn&&(!o||null!=a[0]&&null!=a[1]||(a={min:null==a[0]?i:{mode:1,hard:a[0],soft:a[0]},max:null==a[1]?i:{mode:1,hard:a[1],soft:a[1]}},o=!1),!o&&V(a))){var s=a;a=(n,e,r)=>null==e?N:u(e,r,s)}r.range=T(a||(l?yr:n==Cn?3==r.distr?zr:4==r.distr?Er:kr:3==r.distr?Sr:4==r.distr?Dr:Mr)),r.auto=T(!o&&r.auto),r.clamp=T(r.clamp||je),r._min=r._max=null}}}for(var Ln in Rn("x"),Rn("y"),Dn.forEach((n=>{Rn(n.scale)})),Tn.forEach((n=>{Rn(n.scale)})),e.scales)Rn(Ln);var In,Gn,Nn=An[Cn],On=Nn.distr;0==Nn.ori?(_n(A,"u-hz"),In=c,Gn=v):(_n(A,"u-vt"),In=v,Gn=c);var jn={};for(var Vn in An){var Un=An[Vn];null==Un.min&&null==Un.max||(jn[Vn]={min:Un.min,max:Un.max},Un.min=Un.max=null)}var Zn,Kn=e.tzDate||(n=>new Date(g(n/Sn))),Xn=e.fmtDate||Bn,Qn=1==Sn?ue(Kn):he(Kn),$n=pe(Kn,de(1==Sn?se:ve,Xn)),ne=xe(Kn,ge("{YYYY}-{MM}-{DD} {h}:{mm}{aa}",Xn)),ee=[],re=o.legend=q({},we,e.legend),te=re.show,le=re.markers;re.idxs=ee,le.width=T(le.width),le.dash=T(le.dash),le.stroke=T(le.stroke),le.fill=T(le.fill);var ae,ie=[],fe=[],me=!1,_e={};if(re.live){var be=Dn[1]?Dn[1].values:null;for(var ke in ae=(me=null!=be)?be(o,1,0):{_:0})_e[ke]="--"}if(te)if(Zn=yn("table","u-legend",A),me){var Me=yn("tr","u-thead",Zn);for(var Se in yn("th",null,Me),ae)yn("th",gn,Me).textContent=Se}else _n(Zn,"u-inline"),re.live&&_n(Zn,"u-live");var ze={show:!0},De={show:!1},Ce=new Map;function Fe(n,e,r){var t=Ce.get(e)||{},l=cr.bind[n](o,e,r);l&&(Pn(n,e,t[n]=l),Ce.set(e,t))}function He(n,e){var r=Ce.get(e)||{};for(var t in r)null!=n&&t!=n||(Wn(t,e,r[t]),delete r[t]);null==n&&Ce.delete(e)}var Re=0,Je=0,Ze=0,Ke=0,Xe=0,Qe=0,nr=0,er=0,rr=0,tr=0;o.bbox={};var lr=!1,ar=!1,ir=!1,or=!1,sr=!1;function ur(n,e,r){(r||n!=o.width||e!=o.height)&&fr(n,e),Qr(!1),ir=!0,ar=!0,or=sr=cr.left>=0,vt()}function fr(n,e){o.width=Re=Ze=n,o.height=Je=Ke=e,Xe=Qe=0,function(){var n=!1,e=!1,r=!1,t=!1;Tn.forEach((l=>{if(l.show&&l._show){var a=l.side,i=a%2,o=l._size+(l.labelSize=null!=l.label?l.labelSize||30:0);o>0&&(i?(Ze-=o,3==a?(Xe+=o,t=!0):r=!0):(Ke-=o,0==a?(Qe+=o,n=!0):e=!0))}})),Pr[0]=n,Pr[1]=r,Pr[2]=e,Pr[3]=t,Ze-=Fr[1]+Fr[3],Xe+=Fr[3],Ke-=Fr[2]+Fr[0],Qe+=Fr[0]}(),function(){var n=Xe+Ze,e=Qe+Ke,r=Xe,t=Qe;function l(l,a){switch(l){case 1:return(n+=a)-a;case 2:return(e+=a)-a;case 3:return(r-=a)+a;case 0:return(t-=a)+a}}Tn.forEach((n=>{if(n.show&&n._show){var e=n.side;n._pos=l(e,n._size),null!=n.label&&(n._lpos=l(e,n.labelSize))}}))}();var r=o.bbox;nr=r.left=D(Xe*K,.5),er=r.top=D(Qe*K,.5),rr=r.width=D(Ze*K,.5),tr=r.height=D(Ke*K,.5)}o.setSize=function(n){ur(n.width,n.height)};var cr=o.cursor=q({},ye,e.cursor);cr.idxs=ee,cr._lock=!1;var vr=cr.points;vr.show=T(vr.show),vr.size=T(vr.size),vr.stroke=T(vr.stroke),vr.width=T(vr.width),vr.fill=T(vr.fill);var hr=o.focus=q({},e.focus||{alpha:.3},cr.focus),dr=hr.prox>=0,pr=[null];function gr(n,e){var r=An[n.scale].time,t=n.value;if(n.value=r?j(t)?xe(Kn,ge(t,Xn)):t||ne:t||Ie,n.label=n.label||(r?"Time":"Value"),e>0){n.width=null==n.width?1:n.width,n.paths=n.paths||xr||W,n.fillTo=T(n.fillTo||Ne),n.pxAlign=+f(n.pxAlign,an),n.pxRound=$e(n.pxAlign),n.stroke=T(n.stroke||null),n.fill=T(n.fill||null),n._stroke=n._fill=n._paths=n._focus=null;var l=Be(n.width,1),a=n.points=q({},{size:l,width:_(1,.2*l),stroke:n.stroke,space:2*l,paths:wr,_stroke:null,_fill:null},n.points);a.show=T(a.show),a.filter=T(a.filter),a.fill=T(a.fill),a.stroke=T(a.stroke),a.paths=T(a.paths),a.pxAlign=n.pxAlign}if(te){var i=function(n,e){if(0==e&&(me||!re.live))return N;var r=[],t=yn("tr","u-series",Zn,Zn.childNodes[e]);_n(t,n.class),n.show||_n(t,mn);var l=yn("th",null,t);if(le.show){var a=Mn("u-marker",l);if(e>0){var i=le.width(o,e);i&&(a.style.border=i+"px "+le.dash(o,e)+" "+le.stroke(o,e)),a.style.background=le.fill(o,e)}}var s=Mn(gn,l);for(var u in s.textContent=n.label,e>0&&(le.show||(s.style.color=n.width>0?le.stroke(o,e):le.fill(o,e)),Fe("click",l,(e=>{if(!cr._lock){var r=Dn.indexOf(n);if(e.ctrlKey!=re.isolate){var t=Dn.some(((n,e)=>e>0&&e!=r&&n.show));Dn.forEach(((n,e)=>{e>0&&zt(e,t?e==r?ze:De:ze,Zt.setSeries)}))}else zt(r,{show:!n.show},Zt.setSeries)}})),dr&&Fe(cn,l,(()=>{cr._lock||zt(Dn.indexOf(n),Dt,Zt.setSeries)}))),ae){var f=yn("td","u-value",t);f.textContent="--",r.push(f)}return[t,r]}(n,e);ie.splice(e,0,i[0]),fe.splice(e,0,i[1]),re.values.push(null)}if(cr.show){ee.splice(e,0,null);var s=function(n,e){if(e>0){var r=cr.points.show(o,e);if(r)return _n(r,"u-cursor-pt"),_n(r,n.class),zn(r,-10,-10,Ze,Ke),X.insertBefore(r,pr[e]),r}}(n,e);s&&pr.splice(e,0,s)}}o.addSeries=function(n,e){n=br(n,e=null==e?Dn.length:e,Te,Oe),Dn.splice(e,0,n),gr(Dn[e],e)},o.delSeries=function(n){if(Dn.splice(n,1),te){re.values.splice(n,1),fe.splice(n,1);var e=ie.splice(n,1)[0];He(null,e.firstChild),e.remove()}cr.show&&(ee.splice(n,1),pr.length>1&&pr.splice(n,1)[0].remove())};var Pr=[!1,!1,!1,!1];function Wr(n,e,r){var t=r[0],l=r[1],a=r[2],i=r[3],o=e%2,s=0;return 0==o&&(i||l)&&(s=0==e&&!t||2==e&&!a?g(Ee.size/3):0),1==o&&(t||a)&&(s=1==e&&!l||3==e&&!i?g(Ge.size/2):0),s}var Yr,Cr=o.padding=(e.padding||[Wr,Wr,Wr,Wr]).map((n=>T(f(n,Wr)))),Fr=o._padding=Cr.map(((n,e)=>n(o,e,Pr,0))),Hr=null,Rr=null,Lr=Dn[0].idxs,Ir=null,Gr=!1;function Br(n,e){if((r=(n||[]).slice())[0]=r[0]||[],o.data=r.slice(),Yr=(Ir=r[0]).length,2==On&&(r[0]=Ir.map(((n,e)=>e))),o._data=r,Qr(!0),qt("setData"),!1!==e){var t=Nn;t.auto(o,Gr)?Nr():St(Cn,t.min,t.max),or=cr.left>=0,sr=!0,vt()}}function Nr(){var n,e,t,i,o;Gr=!0,Yr>0?(Hr=Lr[0]=0,Rr=Lr[1]=Yr-1,i=r[0][Hr],o=r[0][Rr],2==On?(i=Hr,o=Rr):1==Yr&&(3==On?(i=(n=l(i,i,Nn.log,!1))[0],o=n[1]):4==On?(i=(e=a(i,i,Nn.log,!1))[0],o=e[1]):Nn.time?o=i+g(86400/Sn):(i=(t=u(i,o,.1,!0))[0],o=t[1]))):(Hr=Lr[0]=i=null,Rr=Lr[1]=o=null),St(Cn,i,o)}function Or(n,e,r,t,l){I.strokeStyle=n||on,I.lineWidth=e,I.lineJoin="round",I.lineCap=t||"butt",I.setLineDash(r||[]),I.fillStyle=l||on}function jr(n,e){var r=e?Dn[n].points:Dn[n];r._stroke=r.stroke(o,n),r._fill=r.fill(o,n)}function Vr(n,e){var r=e?Dn[n].points:Dn[n],t=r._stroke,l=r._fill,a=r._paths,i=a.stroke,s=a.fill,u=a.clip,f=a.flags,c=R(r.width*K,3),v=c%2/2;e&&null==l&&(l=c>0?"#fff":t),I.globalAlpha=r.alpha;var h=1==r.pxAlign;if(h&&I.translate(v,v),I.save(),!e){var d=nr,p=er,m=rr,g=tr,x=c*K/2;0==r.min&&(g+=x),0==r.max&&(p-=x,g+=x),I.beginPath(),I.rect(d,p,m,g),I.clip()}u&&I.clip(u),e?Ur(t,c,r.dash,r.cap,l,i,s,null,f):function(n,e,r,t,l,a,i,s,u){var f=!1;Yn.forEach(((c,v)=>{if(c.series[0]==n){var h=Dn[c.series[1]],d=(h._paths||B).band;I.save();var p=null;h.show&&d?p=c.fill(o,v)||a:d=null,Ur(e,r,t,l,p,i,s,d,u),I.restore(),f=!0}})),f||Ur(e,r,t,l,a,i,s,null,u)}(n,t,c,r.dash,r.cap,l,i,s,f),I.restore(),h&&I.translate(-v,-v),I.globalAlpha=1}function Ur(n,e,r,t,l,a,i,o,s){Or(n,e,r,t,l),o?3==(3&s)?(I.clip(o),qr(l,i),Jr(n,a,e)):2&s?(qr(l,i),I.clip(o),Jr(n,a,e)):1&s&&(I.save(),I.clip(o),qr(l,i),I.restore(),Jr(n,a,e)):(qr(l,i),Jr(n,a,e))}function Jr(n,e,r){n&&e&&r&&I.stroke(e)}function qr(n,e){n&&e&&I.fill(e)}function Zr(n,e,r,t,l,a,i,o,s,u){var f=i%2/2;1==an&&I.translate(f,f),Or(o,i,s,u),I.beginPath();var c,v,h,d,p=l+(0==t||3==t?-a:a);0==r?(v=l,d=p):(c=l,h=p),n.forEach(((n,t)=>{null!=e[t]&&(0==r?c=h=n:v=d=n,I.moveTo(c,v),I.lineTo(h,d))})),I.stroke(),1==an&&I.translate(-f,-f)}function Kr(n){var e=!0;return Tn.forEach(((r,t)=>{if(r.show){var l=An[r.scale];if(null!=l.min){r._show||(e=!1,r._show=!0,Qr(!1));var a=r.side,i=l.min,s=l.max,u=function(n,e,r,t){var l,a=Tn[n];if(t>0){var i=a._space=a.space(o,n,e,r,t),s=a._incrs=a.incrs(o,n,e,r,t,i);l=a._found=function(n,e,r,t,l){for(var a=t/(e-n),i=(""+m(n)).length,o=0;r.length>o;o++){var s=r[o]*a,u=10>r[o]?L.get(r[o]):0;if(s>=l&&17>i+u)return[r[o],s]}return[0,0]}(e,r,s,t,i)}else l=[0,0];return l}(t,i,s,0==a%2?Ze:Ke),f=u[0],c=u[1];if(0!=c){var v=r._splits=r.splits(o,t,i,s,f,c,2==l.distr),h=2==l.distr?v.map((n=>Ir[n])):v,d=2==l.distr?Ir[v[1]]-Ir[v[0]]:f,p=r._values=r.values(o,r.filter(o,h,t,c,d),t,c,d);r._rotate=2==a?r.rotate(o,p,t,c):0;var g=r._size;r._size=x(r.size(o,p,t,n)),null!=g&&r._size!=g&&(e=!1)}}else r._show&&(e=!1,r._show=!1,Qr(!1))}})),e}function Xr(n){var e=!0;return Cr.forEach(((r,t)=>{var l=r(o,t,Pr,n);l!=Fr[t]&&(e=!1),Fr[t]=l})),e}function Qr(n){Dn.forEach(((e,r)=>{r>0&&(e._paths=null,n&&(e.min=null,e.max=null))}))}o.setData=Br;var $r,nt,et,rt,tt,lt,at,it,ot,st,ut,ft,ct=!1;function vt(){ct||(Q(ht),ct=!0)}function ht(){lr&&(function(){var e=J(An,U);for(var t in e){var l=e[t],a=jn[t];if(null!=a&&null!=a.min)q(l,a),t==Cn&&Qr(!0);else if(t!=Cn)if(0==Yr&&null==l.from){var i=l.range(o,null,null,t);l.min=i[0],l.max=i[1]}else l.min=z,l.max=-z}if(Yr>0)for(var s in Dn.forEach(((t,l)=>{var a=t.scale,i=e[a],s=jn[a];if(0==l){var u=i.range(o,i.min,i.max,a);i.min=u[0],i.max=u[1],Hr=n(i.min,r[0]),Rr=n(i.max,r[0]),i.min>r[0][Hr]&&Hr++,r[0][Rr]>i.max&&Rr--,t.min=Ir[Hr],t.max=Ir[Rr]}else if(t.show&&t.auto&&i.auto(o,Gr)&&(null==s||null==s.min)){var f=null==t.min?3==i.distr?function(n,e,r){for(var t=z,l=-z,a=e;r>=a;a++)n[a]>0&&(t=w(t,n[a]),l=_(l,n[a]));return[t==z?1:t,l==-z?10:l]}(r[l],Hr,Rr):function(n,e,r,t){var l=z,a=-z;if(1==t)l=n[e],a=n[r];else if(-1==t)l=n[r],a=n[e];else for(var i=e;r>=i;i++)null!=n[i]&&(l=w(l,n[i]),a=_(a,n[i]));return[l,a]}(r[l],Hr,Rr,t.sorted):[t.min,t.max];i.min=w(i.min,t.min=f[0]),i.max=_(i.max,t.max=f[1])}t.idxs[0]=Hr,t.idxs[1]=Rr})),e){var u=e[s],f=jn[s];if(null==u.from&&(null==f||null==f.min)){var c=u.range(o,u.min==z?null:u.min,u.max==-z?null:u.max,s);u.min=c[0],u.max=c[1]}}for(var v in e){var h=e[v];if(null!=h.from){var d=e[h.from],p=h.range(o,d.min,d.max,v);h.min=p[0],h.max=p[1]}}var m={},g=!1;for(var x in e){var b=e[x],k=An[x];if(k.min!=b.min||k.max!=b.max){k.min=b.min,k.max=b.max;var M=k.distr;k._min=3==M?y(k.min):4==M?S(k.min,k.asinh):k.min,k._max=3==M?y(k.max):4==M?S(k.max,k.asinh):k.max,m[x]=g=!0}}if(g){for(var D in Dn.forEach((n=>{m[n.scale]&&(n._paths=null)})),m)ir=!0,qt("setScale",D);cr.show&&(or=sr=cr.left>=0)}for(var E in jn)jn[E]=null}(),lr=!1),ir&&(function(){for(var n=!1,e=0;!n;){var r=Kr(++e),t=Xr(e);(n=3==e||r&&t)||(fr(o.width,o.height),ar=!0)}}(),ir=!1),ar&&(kn(Z,tn,Xe),kn(Z,en,Qe),kn(Z,$,Ze),kn(Z,nn,Ke),kn(X,tn,Xe),kn(X,en,Qe),kn(X,$,Ze),kn(X,nn,Ke),kn(G,$,Re),kn(G,nn,Je),H.width=g(Re*K),H.height=g(Je*K),Lt(!1),qt("setSize"),ar=!1),Re>0&&Je>0&&(I.clearRect(0,0,H.width,H.height),qt("drawClear"),Hn.forEach((n=>n())),qt("draw")),cr.show&&or&&(Ht(),or=!1),M||(M=!0,o.status=1,qt("ready")),Gr=!1,ct=!1}function dt(e,t){var l=An[e];if(null==l.from){if(0==Yr){var a=l.range(o,t.min,t.max,e);t.min=a[0],t.max=a[1]}if(t.min>t.max){var i=t.min;t.min=t.max,t.max=i}if(Yr>1&&null!=t.min&&null!=t.max&&1e-16>t.max-t.min)return;e==Cn&&2==l.distr&&Yr>0&&(t.min=n(t.min,r[0]),t.max=n(t.max,r[0])),jn[e]=t,lr=!0,vt()}}o.redraw=(n,e)=>{ir=e||!1,!1!==n?St(Cn,Nn.min,Nn.max):vt()},o.setScale=dt;var pt=!1,mt=cr.drag,gt=mt.x,xt=mt.y;cr.show&&(cr.x&&($r=Mn("u-cursor-x",X)),cr.y&&(nt=Mn("u-cursor-y",X)),0==Nn.ori?(et=$r,rt=nt):(et=nt,rt=$r),ut=cr.left,ft=cr.top);var wt,_t,bt,kt=o.select=q({show:!0,over:!0,left:0,width:0,top:0,height:0},e.select),yt=kt.show?Mn("u-select",kt.over?X:Z):null;function Mt(n,e){if(kt.show){for(var r in n)kn(yt,r,kt[r]=n[r]);!1!==e&&qt("setSelect")}}function St(n,e,r){dt(n,{min:e,max:r})}function zt(n,e,r){var t=Dn[n];null!=e.focus&&function(n){if(n!=bt){var e=null==n,r=1!=hr.alpha;Dn.forEach(((t,l)=>{var a=e||0==l||l==n;t._focus=e?null:a,r&&function(n,e){Dn[n].alpha=e,cr.show&&pr[n]&&(pr[n].style.opacity=e),te&&ie[n]&&(ie[n].style.opacity=e)}(l,a?1:hr.alpha)})),bt=n,r&&vt()}}(n),null!=e.show&&(t.show=e.show,function(n){var e=te?ie[n]:null;Dn[n].show?e&&bn(e,mn):(e&&_n(e,mn),pr.length>1&&zn(pr[n],-10,-10,Ze,Ke))}(n),St(t.scale,null,null),vt()),qt("setSeries",n,e),r&&Qt("setSeries",o,n,e)}o.setSelect=Mt,o.setSeries=zt,o.addBand=function(n,e){n.fill=T(n.fill||null),Yn.splice(e=null==e?Yn.length:e,0,n)},o.setBand=function(n,e){q(Yn[n],e)},o.delBand=function(n){null==n?Yn.length=0:Yn.splice(n,1)};var Dt={focus:!0},Et={focus:!1};function Tt(n,e,r){var t=An[e];r&&(n=n/K-(1==t.ori?Qe:Xe));var l=Ze;1==t.ori&&(n=(l=Ke)-n),-1==t.dir&&(n=l-n);var a=t._min,i=a+n/l*(t._max-a),o=t.distr;return 3==o?b(10,i):4==o?((n,e)=>(void 0===e&&(e=1),h.sinh(n/e)))(i,t.asinh):i}function At(n,e){kn(yt,tn,kt.left=n),kn(yt,$,kt.width=e)}function Pt(n,e){kn(yt,en,kt.top=n),kn(yt,nn,kt.height=e)}te&&dr&&Pn(vn,Zn,(()=>{cr._lock||(zt(null,Et,Zt.setSeries),Ht())})),o.valToIdx=e=>n(e,r[0]),o.posToIdx=function(e,t){return n(Tt(e,Cn,t),r[0],Hr,Rr)},o.posToVal=Tt,o.valToPos=(n,e,r)=>0==An[e].ori?c(n,An[e],r?rr:Ze,r?nr:0):v(n,An[e],r?tr:Ke,r?er:0),o.batch=function(n){n(o),vt()},o.setCursor=(n,e)=>{ut=n.left,ft=n.top,Ht(null,null,e)};var Wt=0==Nn.ori?At:Pt,Yt=1==Nn.ori?At:Pt;function Ct(n,e){if(null!=n){var r=n.idx;re.idx=r,Dn.forEach(((n,e)=>{(e>0||!me)&&Ft(e,r)}))}te&&re.live&&function(){if(te&&re.live)for(var n=0;Dn.length>n;n++)if(0!=n||!me){var e=re.values[n],r=0;for(var t in e)fe[n][r++].firstChild.nodeValue=e[t]}}(),sr=!1,!1!==e&&qt("setLegend")}function Ft(n,e){var t;if(null==e)t=_e;else{var l=Dn[n],a=0==n&&2==On?Ir:r[n];t=me?l.values(o,n,e):{_:l.value(o,a[e],n,e)}}re.values[n]=t}function Ht(e,t,l){var a,i;ot=ut,st=ft,a=cr.move(o,ut,ft),ut=a[0],ft=a[1],cr.show&&(et&&zn(et,g(ut),0,Ze,Ke),rt&&zn(rt,0,g(ft),Ze,Ke)),wt=z;var s=0==Nn.ori?Ze:Ke,u=1==Nn.ori?Ze:Ke;if(0>ut||0==Yr||Hr>Rr){i=null;for(var f=0;Dn.length>f;f++)f>0&&pr.length>1&&zn(pr[f],-10,-10,Ze,Ke);if(dr&&zt(null,Dt,Zt.setSeries),re.live){ee.fill(null),sr=!0;for(var c=0;Dn.length>c;c++)re.values[c]=_e}}else{var v=Tt(0==Nn.ori?ut:ft,Cn);i=n(v,r[0],Hr,Rr);for(var h=F(In(r[0][i],Nn,s,0),.5),d=0;Dn.length>d;d++){var m=Dn[d],x=cr.dataIdx(o,d,i,v),_=r[d][x];sr=sr||_!=r[d][ee[d]],ee[d]=x;var b=x==i?h:F(In(r[0][x],Nn,s,0),.5);if(d>0&&m.show){var k=null==_?-10:F(Gn(_,An[m.scale],u,0),.5);if(k>0){var y=p(k-ft);y>wt||(wt=y,_t=d)}var S=void 0,D=void 0;0==Nn.ori?(S=b,D=k):(S=k,D=b),sr&&pr.length>1&&(zn(pr[d],S,D,Ze,Ke),En(pr[d],cr.points.fill(o,d),cr.points.stroke(o,d)))}if(re.live){if(!sr||0==d&&me)continue;Ft(d,x)}}}if(sr&&(re.idx=i,Ct()),kt.show&&pt)if(null!=t){var E=Zt.scales,T=E[0],A=E[1],P=Zt.match,W=P[1],Y=t.cursor.sync.scales,C=Y[0],H=Y[1],R=t.cursor.drag;gt=R._x,xt=R._y;var L,I,G,B,N,O=t.select,j=O.left,V=O.top,U=O.width,J=O.height,q=t.scales[T].ori,Z=t.posToVal,K=null!=T&&(0,P[0])(T,C),X=null!=A&&W(A,H);K&&(0==q?(L=j,I=U):(L=V,I=J),gt?(G=An[T],B=In(Z(L,C),G,s,0),N=In(Z(L+I,C),G,s,0),Wt(w(B,N),p(N-B))):Wt(0,s),X||Yt(0,u)),X&&(1==q?(L=j,I=U):(L=V,I=J),xt?(G=An[A],B=Gn(Z(L,H),G,u,0),N=Gn(Z(L+I,H),G,u,0),Yt(w(B,N),p(N-B))):Yt(0,u),K||Wt(0,s))}else{var Q=p(ot-tt),$=p(st-lt);if(1==Nn.ori){var nn=Q;Q=$,$=nn}gt=mt.x&&Q>=mt.dist,xt=mt.y&&$>=mt.dist;var en,rn,tn=mt.uni;null!=tn?gt&&xt&&(xt=$>=tn,(gt=Q>=tn)||xt||($>Q?xt=!0:gt=!0)):mt.x&&mt.y&&(gt||xt)&&(gt=xt=!0),gt&&(0==Nn.ori?(en=at,rn=ut):(en=it,rn=ft),Wt(w(en,rn),p(rn-en)),xt||Yt(0,u)),xt&&(1==Nn.ori?(en=at,rn=ut):(en=it,rn=ft),Yt(w(en,rn),p(rn-en)),gt||Wt(0,s)),gt||xt||(Wt(0,0),Yt(0,0))}if(cr.idx=i,cr.left=ut,cr.top=ft,mt._x=gt,mt._y=xt,null!=e){if(null!=Kt){var ln=Zt.scales,an=ln[0],on=ln[1];Zt.values[0]=null!=an?Tt(0==Nn.ori?ut:ft,an):null,Zt.values[1]=null!=on?Tt(1==Nn.ori?ut:ft,on):null}if(Qt(sn,o,ut,ft,Ze,Ke,i),dr){var un=Zt.setSeries,fn=hr.prox;null==bt?wt>fn||zt(_t,Dt,un):wt>fn?zt(null,Dt,un):_t!=bt&&zt(_t,Dt,un)}}M&&!1!==l&&qt("setCursor")}o.setLegend=Ct;var Rt=null;function Lt(n){!0===n?Rt=null:qt("syncRect",Rt=X.getBoundingClientRect())}function It(n,e,r,t,l,a){cr._lock||(Gt(n,e,r,t,l,a,0,!1,null!=n),null!=n?Ht(1):Ht(null,e))}function Gt(n,e,r,t,l,a,i,s,u){var f;if(null==Rt&&Lt(!1),null!=n)r=n.clientX-Rt.left,t=n.clientY-Rt.top;else{if(0>r||0>t)return ut=-10,void(ft=-10);var c=Zt.scales,v=c[0],h=c[1],d=e.cursor.sync,p=d.values,m=p[0],g=p[1],x=d.scales,w=x[0],_=x[1],b=Zt.match,y=b[1],M=1==e.scales[w].ori,S=0==Nn.ori?Ze:Ke,z=1==Nn.ori?Ze:Ke,E=M?a:l,T=M?l:a,A=M?t:r,P=M?r:t;if(r=null!=w?(0,b[0])(v,w)?k(m,An[v],S,0):-10:S*(A/E),t=null!=_?y(h,_)?k(g,An[h],z,0):-10:z*(P/T),1==Nn.ori){var W=r;r=t,t=W}}u&&(r>1&&Ze-1>r||(r=D(r,Ze)),t>1&&Ke-1>t||(t=D(t,Ke))),s?(tt=r,lt=t,f=cr.move(o,r,t),at=f[0],it=f[1]):(ut=r,ft=t)}function Bt(){Mt({width:0,height:0},!1)}function Nt(n,e,r,t,l,a){pt=!0,gt=xt=mt._x=mt._y=!1,Gt(n,e,r,t,l,a,0,!0,!1),null!=n&&(Fe(fn,xn,Ot),Qt(un,o,at,it,Ze,Ke,null))}function Ot(n,e,r,t,l,a){pt=mt._x=mt._y=!1,Gt(n,e,r,t,l,a,0,!1,!0);var i=kt.left,s=kt.top,u=kt.width,f=kt.height,c=u>0||f>0;if(c&&Mt(kt),mt.setScale&&c){var v=i,h=u,d=s,p=f;if(1==Nn.ori&&(v=s,h=f,d=i,p=u),gt&&St(Cn,Tt(v,Cn),Tt(v+h,Cn)),xt)for(var m in An){var g=An[m];m!=Cn&&null==g.from&&g.min!=z&&St(m,Tt(d+p,m),Tt(d,m))}Bt()}else cr.lock&&(cr._lock=!cr._lock,cr._lock||Ht());null!=n&&(He(fn,xn),Qt(fn,o,ut,ft,Ze,Ke,null))}function jt(n){Nr(),Bt(),null!=n&&Qt(hn,o,ut,ft,Ze,Ke,null)}function Vt(){Tn.forEach(Ar),ur(o.width,o.height,!0)}Pn(pn,wn,Vt);var Ut={};Ut.mousedown=Nt,Ut.mousemove=It,Ut.mouseup=Ot,Ut.dblclick=jt,Ut.setSeries=(n,e,r,t)=>{zt(r,t)},cr.show&&(Fe(un,X,Nt),Fe(sn,X,It),Fe(cn,X,Lt),Fe(vn,X,(function(){if(!cr._lock){var n=pt;if(pt){var e,r,t=!0,l=!0;0==Nn.ori?(e=gt,r=xt):(e=xt,r=gt),e&&r&&(t=10>=ut||ut>=Ze-10,l=10>=ft||ft>=Ke-10),e&&t&&(ut=at>ut?0:Ze),r&&l&&(ft=it>ft?0:Ke),Ht(1),pt=!1}ut=-10,ft=-10,Ht(1),n&&(pt=n)}})),Fe(hn,X,jt),mr.add(o),o.syncRect=Lt);var Jt=o.hooks=e.hooks||{};function qt(n,e,r){n in Jt&&Jt[n].forEach((n=>{n.call(null,o,e,r)}))}(e.plugins||[]).forEach((n=>{for(var e in n.hooks)Jt[e]=(Jt[e]||[]).concat(n.hooks[e])}));var Zt=q({key:null,setSeries:!1,filters:{pub:Y,sub:Y},scales:[Cn,Dn[1]?Dn[1].scale:null],match:[C,C],values:[null,null]},cr.sync);cr.sync=Zt;var Kt=Zt.key,Xt=qe(Kt);function Qt(n,e,r,t,l,a,i){Zt.filters.pub(n,e,r,t,l,a,i)&&Xt.pub(n,e,r,t,l,a,i)}function $t(){qt("init",e,r),Br(r||e.data,!1),jn[Cn]?dt(Cn,jn[Cn]):Nr(),ur(e.width,e.height),Ht(),Mt(kt,!1)}return Xt.sub(o),o.pub=function(n,e,r,t,l,a,i){Zt.filters.sub(n,e,r,t,l,a,i)&&Ut[n](null,e,r,t,l,a,i)},o.destroy=function(){Xt.unsub(o),mr.delete(o),Ce.clear(),Wn(pn,wn,Vt),A.remove(),qt("destroy")},Dn.forEach(gr),Tn.forEach((function(n,e){if(n._show=n.show,n.show){var r=An[n.scale];null==r&&(n.scale=n.side%2?Dn[1].scale:Cn,r=An[n.scale]);var t=r.time;n.size=T(n.size),n.space=T(n.space),n.rotate=T(n.rotate),n.incrs=T(n.incrs||(2==r.distr?Jn:t?1==Sn?oe:ce:qn)),n.splits=T(n.splits||(t&&1==r.distr?Qn:3==r.distr?We:4==r.distr?Ye:Pe)),n.stroke=T(n.stroke),n.grid.stroke=T(n.grid.stroke),n.ticks.stroke=T(n.ticks.stroke);var l=n.values;n.values=O(l)&&!O(l[0])?T(l):t?O(l)?pe(Kn,de(l,Xn)):j(l)?function(n,e){var r=Bn(e);return(e,t)=>t.map((e=>r(n(e))))}(Kn,l):l||$n:l||Ae,n.filter=T(n.filter||(3>r.distr?P:Le)),n.font=Tr(n.font),n.labelFont=Tr(n.labelFont),n._size=n.size(o,null,e,0),n._space=n._rotate=n._incrs=n._found=n._splits=n._values=null,n._size>0&&(Pr[e]=!0)}})),t?t instanceof HTMLElement?(t.appendChild(A),$t()):t(o,$t):$t(),o}Pr.assign=q,Pr.fmtNum=v,Pr.rangeNum=u,Pr.rangeLog=l,Pr.rangeAsinh=a,Pr.orient=Ze,Pr.join=function(n,e){for(var r=new Set,t=0;n.length>t;t++)for(var l=n[t][0],a=l.length,i=0;a>i;i++)r.add(l[i]);for(var o=[Array.from(r).sort(((n,e)=>n-e))],s=o[0].length,u=new Map,f=0;s>f;f++)u.set(o[0][f],f);for(var c=0;n.length>c;c++)for(var v=n[c],h=v[0],d=1;v.length>d;d++){for(var p=v[d],m=Array(s).fill(void 0),g=e?e[c][d]:1,x=[],w=0;p.length>w;w++){var _=p[w],b=u.get(h[w]);null===_?0!=g&&(m[b]=_,2==g&&x.push(b)):m[b]=_}Z(m,x,s),o.push(m)}return o},Pr.fmtDate=Bn,Pr.tzDate=function(n,e){var r;return"UTC"==e||"Etc/UTC"==e?r=new Date(+n+6e4*n.getTimezoneOffset()):e==Nn?r=n:(r=new Date(n.toLocaleString("en-US",{timeZone:e}))).setMilliseconds(n.getMilliseconds()),r},Pr.sync=qe,Pr.addGap=Qe,Pr.clipGaps=Xe;var Wr=Pr.paths={points:fr};return Wr.linear=dr,Wr.stepped=function(n){var r=f(n.align,1),t=f(n.ascDesc,!1);return(n,l,a,i)=>Ze(n,l,((o,s,u,f,c,v,h,d,p,m,g)=>{var x=o.pxRound,w=0==f.ori?rr:tr,_={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:1},b=_.stroke,k=1*f.dir*(0==f.ori?1:-1);a=e(u,a,i,1),i=e(u,a,i,-1);var y=[],M=!1,S=x(h(u[1==k?a:i],c,g,p)),z=x(v(s[1==k?a:i],f,m,d)),D=z;w(b,z,S);for(var E=1==k?a:i;E>=a&&i>=E;E+=k){var T=u[E],A=x(v(s[E],f,m,d));if(null!=T){var P=x(h(T,c,g,p));if(M){if(Qe(y,D,A),S!=P){var W=o.width*K/2,Y=y[y.length-1];Y[0]+=t||1==r?W:-W,Y[1]-=t||-1==r?W:-W}M=!1}1==r?w(b,A,S):w(b,D,P),w(b,A,P),S=P,D=A}else null===T&&(Qe(y,D,A),M=!0)}if(null!=o.fill){var C=_.fill=new Path2D(b),F=x(h(o.fillTo(n,l,o.min,o.max),c,g,p));w(C,D,F),w(C,z,F)}return _.gaps=y=o.gaps(n,l,a,i,y),o.spanGaps||(_.clip=Xe(y,f.ori,d,p,m,g)),n.bands.length>0&&(_.band=Ke(n,l,a,i,b)),_}))},Wr.bars=function(n){var r=f((n=n||B).size,[.6,z,1]),t=n.align||0,l=(n.gap||0)*K,a=1-r[0],i=f(r[1],z)*K,o=f(r[2],1)*K,s=n.disp,u=f(n.each,(()=>{}));return(n,r,f,c)=>Ze(n,r,((v,h,d,m,g,x,b,k,y,M,S)=>{var z,E,T=v.pxRound,A=m.dir*(0==m.ori?1:-1),P=g.dir*(1==g.ori?1:-1),W=0==m.ori?lr:ar,Y=0==m.ori?u:(n,e,r,t,l,a,i)=>{u(n,e,r,l,t,i,a)},C=b(v.fillTo(n,r,v.min,v.max),g,S,y),F=T(v.width*K);if(null!=s){h=s.x0.values(n,r,f,c),2==s.x0.unit&&(h=h.map((e=>n.posToVal(k+e*M,m.key,!0))));var H=s.size.values(n,r,f,c);E=T((E=2==s.size.unit?H[0]*M:x(H[0],m,M,k)-x(0,m,M,k))-F),z=1==A?-F/2:E+F/2}else{var R=M;if(h.length>1)for(var L=1,I=1/0;h.length>L;L++){var G=p(h[L]-h[L-1]);I>G&&(I=G,R=p(x(h[L],m,M,k)-x(h[L-1],m,M,k)))}E=T(w(i,_(o,R-R*a))-F-l),z=(0==t?E/2:t==A?0:E)-t*A*l/2}var B,N={stroke:new Path2D,fill:null,clip:null,band:null,gaps:null,flags:3},O=n.bands.length>0;O&&(N.band=new Path2D,B=D(b(g.max,g,S,y),.5));for(var j=N.stroke,V=N.band,U=1==A?f:c;U>=f&&c>=U;U+=A){var J=d[U];if(null==J){if(!O)continue;var q=e(d,1==A?f:c,U,-A),Z=e(d,U,1==A?c:f,A),X=d[q];J=X+(U-q)/(Z-q)*(d[Z]-X)}var Q=x(2!=m.distr||null!=s?h[U]:U,m,M,k),$=b(J,g,S,y),nn=T(Q-z),en=T(_($,C)),rn=T(w($,C)),tn=en-rn;null!=d[U]&&(W(j,nn,rn,E,tn),Y(n,r,U,nn-F/2,rn-F/2,E+F,tn+F)),O&&(1==P?(en=rn,rn=B):(rn=en,en=B),W(V,nn-F/2,rn+F/2,E+F,(tn=en-rn)-F))}return null!=v.fill&&(N.fill=new Path2D(j)),N}))},Wr.spline=function(){return function(n){return(r,t,l,a)=>Ze(r,t,((i,o,s,u,f,c,v,h,d,p,m)=>{var g,x,w,_=i.pxRound;0==u.ori?(g=nr,w=rr,x=sr):(g=er,w=tr,x=ur);var b=1*u.dir*(0==u.ori?1:-1);l=e(s,l,a,1),a=e(s,l,a,-1);for(var k=[],y=!1,M=_(c(o[1==b?l:a],u,p,h)),S=M,z=[],D=[],E=1==b?l:a;E>=l&&a>=E;E+=b){var T=s[E],A=c(o[E],u,p,h);null!=T?(y&&(Qe(k,S,A),y=!1),z.push(S=A),D.push(v(s[E],f,m,d))):null===T&&(Qe(k,S,A),y=!0)}var P={stroke:n(z,D,g,w,x,_),fill:null,clip:null,band:null,gaps:null,flags:1},W=P.stroke;if(null!=i.fill&&null!=W){var Y=P.fill=new Path2D(W),C=_(v(i.fillTo(r,t,i.min,i.max),f,m,d));w(Y,S,C),w(Y,M,C)}return P.gaps=k=i.gaps(r,t,l,a,k),i.spanGaps||(P.clip=Xe(k,u.ori,h,d,p,m)),r.bands.length>0&&(P.band=Ke(r,t,l,a,W)),P}))}(pr)},Pr}();
