<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> say<PERSON> başlangıcı -->
<section class="content">
  <div class="row">
    <div class="col-md-3"></div>
    <div class="col-md-6">
      <form method="post" id="form" action="<?php echo base_url('admin/musteriAddDatabase'); ?>">
        <div class="card card-primary">
          <div class="card-body">
            <div class="form-group">
              <label for="inputName">İşlem Yapan Personel</label>
              <input type="text" class="form-control" required readonly name="personel" value="<?php echo $this->session->admininfo->name; ?>">
            </div>
            <div class="form-group">
              <label for="inputName"><PERSON><PERSON></label>
              <input type="text" class="form-control" required readonly name="musteri" id="musteri" value="">
            </div>
            <div class="form-group">
              <label for="inputName">Müşteri Adı ve Soyadı</label>  
              <input type="text" name="musteriadi" id="musteriadi" required class="form-control">
            </div>
            <div class="form-group">
              <label for="inputName">Araç Plakası</label>  
              <input type="text" name="plaka" id="plaka" required class="form-control">
            </div>
            <div class="form-group">
              <label for="inputName">Müşteri Telefon Numarası</label>  
              <input type="text" name="musteri_numarasi" required class="form-control">
            </div>
            <div class="form-group">
              <label for="inputName">Kart Depozito Ücreti</label>  
              <input type="text" name="depozito" value="<?php echo $fiyatlar->depozito; ?>" required class="form-control">
            </div>
          </div>
          <!-- /.card-body --> 
        </div>
        <!-- /.card -->
      </div>
    </div>
    <div class="row">
      <div class="col-md-3"></div>
      <div class="col-6">
        <a id="buton" class="btn btn-secondary">İşlem İptali</a>
        <input type="submit" value="Kaydet" class="btn btn-success float-right">
      </div>
    </form>
  </div>
</section>
<!-- /.content -->
<script>
$(document).ready(function(){
  $("#buton").on("click", function(){ // buton idli elemana tıklandığında
    var gonderilenform = $("#form").serialize(); // idsi gonderilenform olan formun içindeki tüm elemanları serileştirdi ve gonderilenform adlı değişken oluşturarak içine attı
    $.ajax({
      url:'<?php echo base_url('admin/musteriiptal') ?>', // serileştirilen değerleri ajax.php dosyasına
      type:'POST', // post metodu ile 
      data:gonderilenform, // yukarıda serileştirdiğimiz gonderilenform değişkeni 
      success:function(e){ // gonderme işlemi başarılı ise e değişkeni ile gelen değerleri aldı
        $("div").html("").html(e); // div elemanını her gönderme işleminde boşalttı ve gelen verileri içine attı
      }
    });
    location.reload();
  });
});
</script>
<script type="text/javascript" src="<?php echo base_url('js/custom2.js') ?>"> </script>
<?php $this->load->view('admin/include/footer'); ?>