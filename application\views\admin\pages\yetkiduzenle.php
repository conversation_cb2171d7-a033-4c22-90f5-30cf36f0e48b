<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> başlangıcı -->
<section class="content">
	<div class="row">
		<div class="col-md-3"></div>
		<div class="col-md-6">
			<form method="post" action="<?php echo base_url('admin/yetkiler'); ?>">
				<div class="card card-primary">
					<div class="card-body">
						<div class="form-group">
							<label for="inputName">Yet<PERSON>i Değiştirilecek Kullanıcıyı Seçiniz</label>
							<div class="col-sm-10">
								<select class="form-control" name="user_name">
									<option selected disabled>Seçiniz...</option>
									<?php foreach ($users as $users) { ?>
										<?php
										$yetki = $users->role;
										if ($yetki != 'Supervisor' AND $yetki != 'Admin' AND $users->is_active != 0) {

											?>
											<option value="<?php echo $users->id; ?>"><?php echo $users->name; ?></option>
										<?php  } ?>
									<?php } ?>
								</select>
							</div>
						</div>

					</div>
					<!-- /.card-body --> 
				</div>
				<!-- /.card -->
			</div>
		</div>
		<div class="row">
			<div class="col-md-3"></div>
			<div class="col-6">
				<input type="submit" value="YETKİ GUNCELLE" class="btn btn-success float-right">
			</div>
		</form>
	</div>
</section>
<!-- /.content -->
<script type="text/javascript" src="<?php echo base_url('js/custom2.js') ?>"> </script>
<?php $this->load->view('admin/include/footer'); ?>

