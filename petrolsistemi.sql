-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Anamakine: 127.0.0.1
-- <PERSON><PERSON><PERSON>: 17 Tem 2024, 09:47:43
-- <PERSON><PERSON><PERSON> sür<PERSON>: 10.4.24-MariaDB
-- P<PERSON> Sürümü: 7.4.29

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Veritabanı: `petrolsistemi`
--

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `aktarimda_son_id`
--

CREATE TABLE `aktarimda_son_id` (
  `id` int(11) NOT NULL,
  `bakiyeyukle_son_id` int(11) NOT NULL,
  `loglar_son_id` int(11) NOT NULL,
  `v_depozito_son_id` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Tablo döküm verisi `aktarimda_son_id`
--

INSERT INTO `aktarimda_son_id` (`id`, `bakiyeyukle_son_id`, `loglar_son_id`, `v_depozito_son_id`) VALUES
(1, 0, 0, 0);

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `bakiye_yukle`
--

CREATE TABLE `bakiye_yukle` (
  `id` int(11) NOT NULL,
  `kart_id` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `islemtipi` varchar(5) COLLATE utf8_turkish_ci NOT NULL DEFAULT '1',
  `personel_id` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `kullanildimi` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `eklenecek_bakiye` int(255) DEFAULT NULL,
  `date_time` datetime DEFAULT NULL,
  `mevcut_dongu_baslangic` time DEFAULT NULL,
  `date_` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

--
-- Tablo döküm verisi `bakiye_yukle`
--

INSERT INTO `bakiye_yukle` (`id`, `kart_id`, `islemtipi`, `personel_id`, `kullanildimi`, `eklenecek_bakiye`, `date_time`, `mevcut_dongu_baslangic`, `date_`) VALUES
(1, '654909b2004c8', '1', 'Technolobal Bilişim', '1', 15, '2023-11-06 18:43:46', '07:00:00', '2023-11-06'),
(2, '65490c8438665', '1', 'Technolobal Bilişim', '1', 15, '2023-11-06 18:55:48', '07:00:00', '2023-11-06'),
(3, '65490c97d99ad', '1', 'Technolobal Bilişim', '1', 15, '2023-11-06 18:56:07', '07:00:00', '2023-11-06'),
(4, 'E5c63cc', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 17:07:17', '07:00:00', '2023-11-09'),
(5, 'E654ce7e1e192', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 17:08:33', '07:00:00', '2023-11-09'),
(6, 'E654ce7f4703a', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 17:08:52', '07:00:00', '2023-11-09'),
(7, 'E654ce8005088', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 17:09:04', '07:00:00', '2023-11-09'),
(8, 'E654ce80cccb8', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 17:09:16', '07:00:00', '2023-11-09'),
(9, 'E654cf9205a17', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 18:22:08', '07:00:00', '2023-11-09'),
(10, 'E654cf92c73b3', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 18:22:20', '07:00:00', '2023-11-09'),
(11, 'E654cfc413a8c', '1', 'Technolobal Bilişim', '1', 15, '2023-11-09 18:35:29', '07:00:00', '2023-11-09');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `cihazlar`
--

CREATE TABLE `cihazlar` (
  `id` int(11) NOT NULL,
  `okuyucusayisi` varchar(255) NOT NULL,
  `supurgesayisi` varchar(255) NOT NULL,
  `havasupurgesayisi` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Tablo döküm verisi `cihazlar`
--

INSERT INTO `cihazlar` (`id`, `okuyucusayisi`, `supurgesayisi`, `havasupurgesayisi`) VALUES
(1, '2', '1', '0');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `cities`
--

CREATE TABLE `cities` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Tablo döküm verisi `cities`
--

INSERT INTO `cities` (`id`, `name`) VALUES
(1, 'Adana'),
(2, 'Adıyaman'),
(3, 'Afyonkarahisar'),
(4, 'Ağrı'),
(5, 'Amasya'),
(6, 'Ankara'),
(7, 'Antalya'),
(8, 'Artvin'),
(9, 'Aydın'),
(10, 'Balıkesir'),
(11, 'Bilecik'),
(12, 'Bingöl'),
(13, 'Bitlis'),
(14, 'Bolu'),
(15, 'Burdur'),
(16, 'Bursa'),
(17, 'Çanakkale'),
(18, 'Çankırı'),
(19, 'Çorum'),
(20, 'Denizli'),
(21, 'Diyarbakır'),
(22, 'Edirne'),
(23, 'Elazığ'),
(24, 'Erzincan'),
(25, 'Erzurum'),
(26, 'Eskişehir'),
(27, 'Gaziantep'),
(28, 'Giresun'),
(29, 'Gümüşhane'),
(30, 'Hakkari'),
(31, 'Hatay'),
(32, 'Isparta'),
(33, 'Mersin'),
(34, 'İstanbul'),
(35, 'İzmir'),
(36, 'Kars'),
(37, 'Kastamonu'),
(38, 'Kayseri'),
(39, 'Kırklareli'),
(40, 'Kırşehir'),
(41, 'Kocaeli'),
(42, 'Konya'),
(43, 'Kütahya'),
(44, 'Malatya'),
(45, 'Manisa'),
(46, 'Kahramanmaraş'),
(47, 'Mardin'),
(48, 'Muğla'),
(49, 'Muş'),
(50, 'Nevşehir'),
(51, 'Niğde'),
(52, 'Ordu'),
(53, 'Rize'),
(54, 'Sakarya'),
(55, 'Samsun'),
(56, 'Siirt'),
(57, 'Sinop'),
(58, 'Sivas'),
(59, 'Tekirdağ'),
(60, 'Tokat'),
(61, 'Trabzon'),
(62, 'Tunceli'),
(63, 'Şanlıurfa'),
(64, 'Uşak'),
(65, 'Van'),
(66, 'Yozgat'),
(67, 'Zonguldak'),
(68, 'Aksaray'),
(69, 'Bayburt'),
(70, 'Karaman'),
(71, 'Kırıkkale'),
(72, 'Batman'),
(73, 'Şırnak'),
(74, 'Bartın'),
(75, 'Ardahan'),
(76, 'Iğdır'),
(77, 'Yalova'),
(78, 'Karabük'),
(79, 'Kilis'),
(80, 'Osmaniye'),
(81, 'Düzce');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `dongubaslangic`
--

CREATE TABLE `dongubaslangic` (
  `id` int(11) NOT NULL,
  `saat` time NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

--
-- Tablo döküm verisi `dongubaslangic`
--

INSERT INTO `dongubaslangic` (`id`, `saat`) VALUES
(1, '07:00:00');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `iade`
--

CREATE TABLE `iade` (
  `id` int(11) NOT NULL,
  `personel` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `kart_id` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `depozito` int(255) DEFAULT NULL,
  `iade_depozito` int(255) DEFAULT 0,
  `iade_bakiye` int(255) DEFAULT 0,
  `date_time` datetime DEFAULT NULL,
  `mevcut_dongu_baslangic` time DEFAULT NULL,
  `date_` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `istasyonlar`
--

CREATE TABLE `istasyonlar` (
  `id` int(11) NOT NULL,
  `istasyon_adi` varchar(255) NOT NULL,
  `istasyon_veritabani_kadi` varchar(255) NOT NULL,
  `istasyon_veritabani_sifre` varchar(255) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Tablo döküm verisi `istasyonlar`
--

INSERT INTO `istasyonlar` (`id`, `istasyon_adi`, `istasyon_veritabani_kadi`, `istasyon_veritabani_sifre`) VALUES
(1, 'Emniyet Bp', 'u411458722_emniyetbp', 'I7=qc1l&'),
(2, 'Kayhan Opet', 'u411458722_kayhanopet', 'IOn.S6rcXu'),
(3, 'Ula Opet', 'u411458722_ulaopet', 'I@HjABh5u#'),
(4, 'Sinpaş Bp', 'u411458722_sinpasbp', ':Yy+o8Yi;'),
(5, 'Kınıklı Opet', 'u411458722_kinikliopet', '0N0iywgz');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `loglar`
--

CREATE TABLE `loglar` (
  `id` int(11) NOT NULL,
  `kart_id` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `calistimi` int(10) NOT NULL,
  `P` int(10) NOT NULL,
  `sure` varchar(100) COLLATE utf8_turkish_ci NOT NULL,
  `eski_bakiye` int(10) NOT NULL,
  `yeni_bakiye` int(10) NOT NULL,
  `okuyucu_numarasi` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `dakikasaniye` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `date` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `musteri`
--

CREATE TABLE `musteri` (
  `id` int(11) NOT NULL,
  `kart_id` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `name` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `telefon` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `bakiye` int(100) NOT NULL,
  `barkod_personel` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `plaka` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

--
-- Tablo döküm verisi `musteri`
--

INSERT INTO `musteri` (`id`, `kart_id`, `name`, `telefon`, `bakiye`, `barkod_personel`, `plaka`) VALUES
(1, '654909b2004c8', 'barkod', '0', 15, 'kopuk', '0'),
(2, '65490c8438665', 'barkod', '0', 15, 'yikama', '0'),
(3, '65490c97d99ad', 'barkod', '0', 15, 'hava', '0'),
(4, NULL, 'barkod', '0', 15, 'yikama', '0'),
(5, 'E5c63cc', 'barkod', '0', 15, 'yikama', '0'),
(6, 'E654ce7e1e192', 'barkod', '0', 15, 'kopuk', '0'),
(7, 'E654ce7f4703a', 'barkod', '0', 15, 'supurge', '0'),
(8, 'E654ce8005088', 'barkod', '0', 15, 'hava', '0'),
(9, 'E654ce80cccb8', 'barkod', '0', 15, 'kopuk', '0'),
(10, 'E654cf9205a17', 'barkod', '0', 15, 'yikama', '0'),
(11, 'E654cf92c73b3', 'barkod', '0', 15, 'supurge', '0'),
(12, 'E654cfc413a8c', 'barkod', '0', 15, 'yikama', '0');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `mustericlone`
--

CREATE TABLE `mustericlone` (
  `id` int(11) NOT NULL,
  `kart_id` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `plaka` varchar(255) NOT NULL DEFAULT '0',
  `telefon` varchar(255) DEFAULT NULL,
  `bakiye` int(100) DEFAULT 0,
  `barkod_personel` varchar(255) DEFAULT NULL,
  `islem` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

--
-- Tablo döküm verisi `mustericlone`
--

INSERT INTO `mustericlone` (`id`, `kart_id`, `name`, `plaka`, `telefon`, `bakiye`, `barkod_personel`, `islem`) VALUES
(1, NULL, 'barkod', '0', '0', 15, 'yikama', 'insert'),
(2, 'E5c63cc', 'barkod', '0', '0', 15, 'yikama', 'insert'),
(3, 'E654ce7e1e192', 'barkod', '0', '0', 15, 'kopuk', 'insert'),
(4, 'E654ce7f4703a', 'barkod', '0', '0', 15, 'supurge', 'insert'),
(5, 'E654ce8005088', 'barkod', '0', '0', 15, 'hava', 'insert'),
(6, 'E654ce80cccb8', 'barkod', '0', '0', 15, 'kopuk', 'insert'),
(7, 'E654cf9205a17', 'barkod', '0', '0', 15, 'yikama', 'insert'),
(8, 'E654cf92c73b3', 'barkod', '0', '0', 15, 'supurge', 'insert'),
(9, 'E654cfc413a8c', 'barkod', '0', '0', 15, 'yikama', 'insert');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `promosyonlar`
--

CREATE TABLE `promosyonlar` (
  `id` int(11) NOT NULL,
  `yukleme_miktari` int(255) NOT NULL,
  `hediye_yikama` int(255) NOT NULL,
  `hediye_kopuk` int(255) NOT NULL,
  `date` varchar(255) COLLATE utf8_turkish_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `sureler`
--

CREATE TABLE `sureler` (
  `id` int(11) NOT NULL,
  `yikamasuresi` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `kopuksuresi` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `supurge` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `hava` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `tarih` varchar(255) COLLATE utf8_turkish_ci NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

--
-- Tablo döküm verisi `sureler`
--

INSERT INTO `sureler` (`id`, `yikamasuresi`, `kopuksuresi`, `supurge`, `hava`, `tarih`) VALUES
(1, '04:00', '01:15', '04:00', '02:00', '09.08.2023');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `ucretler`
--

CREATE TABLE `ucretler` (
  `id` int(11) NOT NULL,
  `yikama` int(100) NOT NULL,
  `kopuk` int(100) NOT NULL,
  `supurge` int(100) DEFAULT NULL,
  `hava` int(100) NOT NULL,
  `depozito` int(100) DEFAULT NULL,
  `date` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

--
-- Tablo döküm verisi `ucretler`
--

INSERT INTO `ucretler` (`id`, `yikama`, `kopuk`, `supurge`, `hava`, `depozito`, `date`) VALUES
(1, 15, 20, 25, 30, 11, '06.11.2023');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `users`
--

CREATE TABLE `users` (
  `id` int(11) NOT NULL,
  `name` varchar(255) COLLATE utf8_turkish_ci NOT NULL,
  `kart_id` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `role` varchar(10) COLLATE utf8_turkish_ci NOT NULL,
  `email` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `password` varchar(255) COLLATE utf8_turkish_ci DEFAULT NULL,
  `is_active` int(5) NOT NULL DEFAULT 1,
  `musteriekle_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '1',
  `musterigoruntule_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `bakiyeyukle_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '1',
  `barkodolustur_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '1',
  `barkodlarigoruntule_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '1',
  `loglarigoruntule_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `fiyatlar_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `sureler_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `raporlar_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `kartiade_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '1',
  `personelekle_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `personelgoruntule_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `musteriduzenle_yetki` varchar(255) COLLATE utf8_turkish_ci NOT NULL DEFAULT '0',
  `mesaisaati_yetki` varchar(255) COLLATE utf8_turkish_ci DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_turkish_ci;

--
-- Tablo döküm verisi `users`
--

INSERT INTO `users` (`id`, `name`, `kart_id`, `role`, `email`, `password`, `is_active`, `musteriekle_yetki`, `musterigoruntule_yetki`, `bakiyeyukle_yetki`, `barkodolustur_yetki`, `barkodlarigoruntule_yetki`, `loglarigoruntule_yetki`, `fiyatlar_yetki`, `sureler_yetki`, `raporlar_yetki`, `kartiade_yetki`, `personelekle_yetki`, `personelgoruntule_yetki`, `musteriduzenle_yetki`, `mesaisaati_yetki`) VALUES
(7, 'topalkeydir', NULL, 'Supervisor', '<EMAIL>', 'denwash20.23>', 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1'),
(11, 'Technolobal Bilişim', NULL, 'Admin', '<EMAIL>', 'denwash20.23!', 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1'),
(36, 'Technolobal Bilişim', NULL, 'Admin', 'a', 'b', 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1');

-- --------------------------------------------------------

--
-- Tablo için tablo yapısı `vardiya_depozito`
--

CREATE TABLE `vardiya_depozito` (
  `id` int(11) NOT NULL,
  `kart_id` varchar(255) DEFAULT NULL,
  `personel_id` varchar(255) DEFAULT NULL,
  `depozito` int(255) DEFAULT NULL,
  `date_time` datetime DEFAULT NULL,
  `mevcut_dongu_baslangic` time DEFAULT NULL,
  `date_` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dökümü yapılmış tablolar için indeksler
--

--
-- Tablo için indeksler `aktarimda_son_id`
--
ALTER TABLE `aktarimda_son_id`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `bakiye_yukle`
--
ALTER TABLE `bakiye_yukle`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `cihazlar`
--
ALTER TABLE `cihazlar`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `cities`
--
ALTER TABLE `cities`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `dongubaslangic`
--
ALTER TABLE `dongubaslangic`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `iade`
--
ALTER TABLE `iade`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `istasyonlar`
--
ALTER TABLE `istasyonlar`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `loglar`
--
ALTER TABLE `loglar`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `musteri`
--
ALTER TABLE `musteri`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `mustericlone`
--
ALTER TABLE `mustericlone`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `promosyonlar`
--
ALTER TABLE `promosyonlar`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `sureler`
--
ALTER TABLE `sureler`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `ucretler`
--
ALTER TABLE `ucretler`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`);

--
-- Tablo için indeksler `vardiya_depozito`
--
ALTER TABLE `vardiya_depozito`
  ADD PRIMARY KEY (`id`);

--
-- Dökümü yapılmış tablolar için AUTO_INCREMENT değeri
--

--
-- Tablo için AUTO_INCREMENT değeri `aktarimda_son_id`
--
ALTER TABLE `aktarimda_son_id`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Tablo için AUTO_INCREMENT değeri `bakiye_yukle`
--
ALTER TABLE `bakiye_yukle`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- Tablo için AUTO_INCREMENT değeri `cihazlar`
--
ALTER TABLE `cihazlar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Tablo için AUTO_INCREMENT değeri `cities`
--
ALTER TABLE `cities`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=82;

--
-- Tablo için AUTO_INCREMENT değeri `dongubaslangic`
--
ALTER TABLE `dongubaslangic`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Tablo için AUTO_INCREMENT değeri `iade`
--
ALTER TABLE `iade`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `istasyonlar`
--
ALTER TABLE `istasyonlar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- Tablo için AUTO_INCREMENT değeri `loglar`
--
ALTER TABLE `loglar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `musteri`
--
ALTER TABLE `musteri`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- Tablo için AUTO_INCREMENT değeri `mustericlone`
--
ALTER TABLE `mustericlone`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- Tablo için AUTO_INCREMENT değeri `promosyonlar`
--
ALTER TABLE `promosyonlar`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- Tablo için AUTO_INCREMENT değeri `sureler`
--
ALTER TABLE `sureler`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Tablo için AUTO_INCREMENT değeri `ucretler`
--
ALTER TABLE `ucretler`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Tablo için AUTO_INCREMENT değeri `users`
--
ALTER TABLE `users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=37;

--
-- Tablo için AUTO_INCREMENT değeri `vardiya_depozito`
--
ALTER TABLE `vardiya_depozito`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
