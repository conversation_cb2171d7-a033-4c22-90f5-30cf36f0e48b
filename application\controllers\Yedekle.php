<?php 
defined('BASEPATH') OR exit('No direct script access allowed');
class <PERSON><PERSON><PERSON> extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
	}
	public function asdasd()//verileri internet yokken atildigi clone databaseden internet geldiginde online sisteme tasiyacak olan function  
	{
		$source_host = 'localhost';
		$source_user = 'root';
		$source_pass = '';
		$source_db = 'petrolsistemi';

		$target_host = '***********';
		$target_user = 'u401251138_test';
		$target_pass = 'J$2wJwki';
		$target_db = 'u401251138_test';
		$target_table = 'musteri';

		$source_conn = mysqli_connect($source_host, $source_user, $source_pass, $source_db);
		if ($source_conn) {
			$target_conn = mysqli_connect($target_host, $target_user, $target_pass, $target_db);
			if ($target_conn) {
				$source_table = 'mustericlone';
				$source_query = "SELECT * FROM $source_table ORDER BY id ASC";
				$source_result = mysqli_query($source_conn, $source_query);
				if ($source_result) {
					while ($row = mysqli_fetch_assoc($source_result)) {
						$islem = $row["islem"];
						if ($islem == "update") {
							$bakiye = $row["bakiye"];
							$kart_id = $row["kart_id"];
							$updatequery = "UPDATE $target_table SET bakiye = '$bakiye' WHERE kart_id = '$kart_id' ";
							$target_result = mysqli_query($target_conn, $updatequery);
							echo "update";
						}elseif ($islem == "delete") {
							$kart_id = $row["kart_id"];
							$deletequery = "DELETE FROM $target_table WHERE kart_id = $kart_id";
							$target_result = mysqli_query($target_conn, $deletequery);
							echo "delete ";
						}elseif ($islem == "insert") {
							$bakiye = $row["bakiye"];
							$kart_id = $row["kart_id"];
							$name = $row["name"];
							$plaka = $row["plaka"];
							$telefon = $row["telefon"];
							$barkod_personel = $row["barkod_personel"];
							$insertquery = "INSERT INTO $target_table (kart_id, name, plaka, telefon, bakiye, barkod_personel)
							VALUES ('$kart_id', '$name', '$plaka', '$telefon', '$bakiye', '$barkod_personel')";
							$target_result = mysqli_query($target_conn, $insertquery);
							echo "insert";
						}
					}
					$truncatequery = "TRUNCATE TABLE $source_table";
					$target_result = mysqli_query($source_conn, $truncatequery);
					echo 'silindi';

				}

			}elseif (!$target_conn) {
				die("Hedef veritabanı bağlantısı başarısız: " . mysqli_connect_error());
			}
		}elseif (!$source_conn) {
			die("Kaynak veritabanı bağlantısı başarısız: " . mysqli_connect_error());
		}

	}
	public function localdisaaktar() // localhosttaki databaseyi yedekleyip bir dosya icerisine .sql uzantili olarak kayit edicektir
	{
		$host = "***********";
		$db_user = "u401251138_test";
		$db_pass = "J$2wJwki";
		$backup_dir = "C:/xampp/htdocs/petrolsistemi/yedekler/";

		$db_names = "u401251138_test";
		$table_name = "musteri";
		$backup_file = $backup_dir . $db_names . "_backup.sql";
		exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name} > {$backup_file}");
		

	}
	public function sqlcanliyaat()//localde daha onceden disa aktarilmis .sql uzantili dosyayi online databaseye ice aktarir
	{
		$servername = "localhost";
		$username = "root";
		$password = "";
		$dbname = "petrolsistemi";

		$conn = new mysqli($servername, $username, $password, $dbname);

		if ($conn->connect_error) {
			die("Bağlantı hatası: " . $conn->connect_error);
		}

		$sqlFile = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

		$sql = file_get_contents($sqlFile);

		$queries = explode(';', $sql);

		foreach ($queries as $query) {
			$query = trim($query);
			if (!empty($query)) {
				$result = $conn->query($query);
				if (!$result) {
					echo "Hata: " . $conn->error;
				}
			}
		}

		echo "SQL dosyası başarıyla içe aktarıldı!";

		$conn->close();

	}
	public function yedeksil()
	{
		$dosyaYolu = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

		if (file_exists($dosyaYolu)) {
			if (unlink($dosyaYolu)) {
				echo "Dosya başarıyla silindi.";
			} else {
				echo "Dosya silinemedi.";
			}
		} else {
			echo "Dosya mevcut değil.";
		}
	}
	public function deneme()
	{
		
//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BASLANGIC
		$host = "***********";
		$db_user = "u401251138_test";
		$db_pass = "J$2wJwki";
		$backup_dir = "C:/xampp/htdocs/petrolsistemi/yedekler/";

		$db_names = "u401251138_test";
		$table_name = "musteri";
		$backup_file = $backup_dir . "musteri_backup.sql";
		exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name} > {$backup_file}");
//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BITIS

//*****************************************************************************************************

//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BASLANGIC
		$servername = "localhost";
		$username = "root";
		$password = "";
		$dbname = "petrolsistemi";

		$conn = new mysqli($servername, $username, $password, $dbname);

		if ($conn->connect_error) {
			die("Bağlantı hatası: " . $conn->connect_error);
		}

		$sqlFile = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

		$sql = file_get_contents($sqlFile);

		$queries = explode(';', $sql);

		foreach ($queries as $query) {
			$query = trim($query);
			if (!empty($query)) {
				$result = $conn->query($query);
				if (!$result) {
					echo "Hata: " . $conn->error;
				}
			}
		}

		echo "SQL dosyası başarıyla içe aktarıldı!";

		$conn->close();
//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BITIS
	}

	
}