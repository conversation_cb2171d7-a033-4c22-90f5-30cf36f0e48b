<?php 


$source_host = 'localhost'; //ara db yollari verilicek buraya
$source_user = 'root';
$source_pass = '';
$source_db = 'petrolsistemi';

$target_host = '***********';
$target_user = 'u411458722_pekdemir';
$target_pass = '?7cy+18Z6:x';
$target_db = 'u411458722_pekdemir';
$target_table = 'musteri';

$source_conn = mysqli_connect($source_host, $source_user, $source_pass, $source_db);
if ($source_conn) {
	$target_conn = mysqli_connect($target_host, $target_user, $target_pass, $target_db);
	if ($target_conn) {
		$source_table = 'mustericlone';
		$source_query = "SELECT * FROM $source_table ORDER BY id ASC";
		$source_result = mysqli_query($source_conn, $source_query);
		if ($source_result) {
			while ($row = mysqli_fetch_assoc($source_result)) {
				$islem = $row["islem"];
				if ($islem == "update") {
					$bakiye = $row["bakiye"];
					$kart_id = $row["kart_id"];
					$updatequery = "UPDATE $target_table SET bakiye = $bakiye WHERE kart_id = $kart_id ";
					$target_result = mysqli_query($target_conn, $updatequery);
					//echo "update";
				}elseif ($islem == "delete") {
					$kart_id = $row["kart_id"];
					$deletequery = "DELETE FROM $target_table WHERE kart_id = $kart_id";
					$target_result = mysqli_query($target_conn, $deletequery);
					//echo "delete ";
				}elseif ($islem == "insert") {
					$bakiye = $row["bakiye"];
					$kart_id = $row["kart_id"];
					$name = $row["name"];
					$plaka = $row["plaka"];
					$telefon = $row["telefon"];
					$barkod_personel = $row["barkod_personel"];
					$insertquery = "INSERT INTO $target_table (kart_id, name, plaka, telefon, bakiye, barkod_personel)
					VALUES ('$kart_id', '$name', '$plaka', '$telefon', '$bakiye', '$barkod_personel')";
					$target_result = mysqli_query($target_conn, $insertquery);
					//echo "insert";
				}
			}
			$truncatequery = "TRUNCATE TABLE $source_table";
			$target_result = mysqli_query($source_conn, $truncatequery);
			//echo 'silindi';

		}

	}elseif (!$target_conn) {
		die("Hedef veritabanı bağlantısı başarısız: " . mysqli_connect_error());
	}
}elseif (!$source_conn) {
	die("Kaynak veritabanı bağlantısı başarısız: " . mysqli_connect_error());
}







?>