<?php 
defined('BASEPATH') OR exit('No direct script access allowed');
class Admin extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->targethost = array(
			'target_host' => '***********',
			'target_user' => 'u411458722_pekdemir',
			'target_pass' => '?7cy+18Z6:x',
			'target_db' => 'u411458722_pekdemir'
		);
		$this->sourcehost= array(
			'source_host' => 'localhost',
			'source_user' => 'root',
			'source_pass' => '',
			'source_db' => 'petrolsistemi'
		);
	}
	
	public function index()
	{
		if ($this->session->userdata('adminlogin')) {redirect('admin/panel');}
		$this->load->view('admin/login');
	}
	public function panel()
	{
		$cihazlar = cihazlar::select();
		$data['okuyucucount'] = $okuyucu = $cihazlar[0]->okuyucusayisi;
		$data['supurgecount'] = $supurge = $cihazlar[0]->supurgesayisi;
		$data['havasupurgecount'] = $havasupurge = $cihazlar[0]->havasupurgesayisi;
		for ($i = 1; $i <= $okuyucu; $i++) {
			$okuyucusay = $data['cihaz'.$i]=loglar::query("SELECT * FROM loglar WHERE okuyucu_numarasi=$i AND calistimi=1 ORDER BY id DESC LIMIT 0,1");
		}
		for ($j = 1; $j <= $supurge; $j++) {
			$supurgesay = $data['supurge'.$j]=loglar::query("SELECT * FROM loglar WHERE okuyucu_numarasi='Supurge$j' AND calistimi=1 ORDER BY id DESC LIMIT 0,1");
		}
		for ($f = 1; $f <= $havasupurge; $f++) {
			$havasay = $data['hava'.$f]=loglar::query("SELECT * FROM loglar WHERE okuyucu_numarasi='Hava$f' AND calistimi=1 ORDER BY id DESC LIMIT 0,1");
		}
		$data['head']="Panel Ekranı";
		$this->load->view('admin/panel',$data);
	}
	public function ajax()
	{
		$cihazlar = cihazlar::select();
		$sureler = sureler::select();
		$okuyucu = $cihazlar[0]->okuyucusayisi;
		$supurge = $cihazlar[0]->supurgesayisi;
		$havasupurge = $cihazlar[0]->havasupurgesayisi;
		for ($i = 1; $i <= $okuyucu; $i++) {
			$cihazcc = 'cihaz'.$i;
			$$cihazcc = $data['cihaz'.$i]=loglar::query("SELECT * FROM loglar WHERE okuyucu_numarasi=$i AND calistimi=1 ORDER BY id DESC LIMIT 0,1");
		}
		for ($j = 1; $j <= $supurge; $j++) {
			$supurgee = 'supurge'.$j;
			$$supurgee = $data['supurge'.$j]=loglar::query("SELECT * FROM loglar WHERE okuyucu_numarasi='Supurge$j' AND calistimi=1 ORDER BY id DESC LIMIT 0,1");
		}
		for ($f = 1; $f <= $havasupurge; $f++) {
			$havaa = 'hava'.$f;
			$$havaa = $data['hava'.$f]=loglar::query("SELECT * FROM loglar WHERE okuyucu_numarasi='hava$f' AND calistimi=1 ORDER BY id DESC LIMIT 0,1");
		}
		for ($i=1; $i <= $okuyucu; $i++) 
		{ 
			$ccc = 'cihaz'.$i;
			$sureokuyucu = 'cihaz'.$i.'sure';
			$cc = 'cc'.$i;
			$c = 'c'.$i;
			if ($$ccc) 
			{
				$$cc = $$ccc[0]->P;
				$$c = 1;
				$$sureokuyucu = $$ccc[0]->dakikasaniye;
			}else
			{
				$$sureokuyucu = 0;
				$$cc = 0;
				$$c = 0;
			}
		}
		for ($j=1; $j <= $supurge; $j++)
		{
			$sss = 'supurge'.$j;
			$suresupurge = 'supurge'.$j.'sure';
			$s = 's'.$j;
			if ($$sss) 
			{
				$$suresupurge = $$sss[0]->dakikasaniye;
				$$s = 1;
			}else
			{
				$$suresupurge = 0;
				$$s = 0;
			}
		}
		for ($f=1; $f <= $havasupurge; $f++)
		{
			$hhh = 'hava'.$f;
			$havasure = 'hava'.$f.'sure';
			$tt = 'hh'.$f;
			$t = 'h'.$f;
			if ($$hhh) 
			{
				$$havasure = $$hhh[0]->dakikasaniye;
				$$tt = $$hhh[0]->P;
				$$t = 1;
			}else
			{
				$$havasure = 0;
				$$tt = 0;
				$$t = 0;
			}
		}
		$data = [];
		$data['okuyucusayisi'] = $okuyucu;
		$data['supurgesayisi'] = $supurge;
		$data['havasayisi'] = $havasupurge;
		for ($i = 1; $i <= $okuyucu; $i++) {
			$data['makine' . $i] = ${'c' . $i};
			$data['makine' . $i . 'p'] = ${'cc' . $i};
			$data['makine' . $i . 'sure'] = ${'cihaz' . $i . 'sure'};
		}
		for ($j=1; $j <= $supurge; $j++) {
			$data['supurge' . $j] = ${'s' . $j};
			$data['supurge' . $j . 'sure'] = ${'supurge' . $j .'sure'};
		}
		for ($f=1; $f <= $havasupurge; $f++) {
			$data['hava' . $f] = ${'h' . $f};
			$data['hava' . $f . 'p'] = ${'hh' . $f};
			$data['hava' . $f . 'sure'] = ${'hava' . $f .'sure'};
		}
		echo json_encode($data);
	}
	public function islemkapat()
	{
		if (isPost()) 
		{
			$ono = postvalue('ono');
			$where = 
			[
				'okuyucu_numarasi' => $ono
			];
			$data = 
			[
				'calistimi' => 2
			];
			loglar::update($where,$data);
			echo json_encode($data);
		}
	}
	public function musterigoruntule()
	{
		$data['musteriler']=musteri::select(['name<>'=>'barkod','kart_id<>'=>'1665213213']);
		$data['head']="Müşteri Görüntüleme Ekranı";
		if ($this->session->admininfo->musterigoruntule_yetki=='1') 
		{
			$this->load->view('admin/pages/musterigoruntule',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Müşteri Görüntüleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}
	}
	public function login()
	{
		$name=Users::find(['name']);
		$exist=Users::find(['email'=>$this->input->post('email'),'password'=>$this->input->post('password')]);
		if ($exist AND $exist->is_active != 0) 
		{
			$this->session->set_userdata('adminlogin',true);
			$this->session->set_userdata('admininfo',$exist);
			redirect('admin/panel');
		}
		else
		{
			$hata="Email adresi veya şifre hatalı.";
			$this->session->set_flashdata('error',$hata);
			redirect('admin');
		}
	}
	//Bakiye İptal Fonksiyonu
	public function bakiyeiptal()
	{
		extract($_POST);
		if (isPost()) 
		{
			$data=
			[
				'kart_id'=>postvalue('musteri'),
				'kullanildimi'=>0
			];
			bakiye::delete($data);
			flash('success','check','Başarılı','İşlem Başarılı Bir Şekilde İptal Edildi');
			redirect($_SERVER['REQUEST_URI'], 'refresh'); 
		}
	}
	//Müşteri İptal Fonksiyonu
	public function musteriiptal()
	{
		extract($_POST);
		if (isPost()) 
		{
			$data=
			[
				'kart_id'=>postvalue('musteri'),
				'kullanildimi'=>3
			];
			bakiye::delete($data);
			flash('success','check','Başarılı','İşlem Başarılı Bir Şekilde İptal Edildi');
			redirect($_SERVER['REQUEST_URI'], 'refresh'); 
		}
	}
	//Çıkış Fonksiyonu
	public function cikis()
	{
		$this->session->sess_destroy();
		redirect('admin');
	}
	public function test()
	{
		$this->load->view('admin/test');
	}
	public function bakiyeekle()
	{
		if (isPost()) 
		{	
			$id = 1;
			$saat = dongubaslangic::find($id);
			$mevcut_dongu_baslangic = $saat->saat;
			$yedekleme = postvalue('musteri');
			$update=
			[
				'id'=>postvalue('id'),
				'kart_id'=>postvalue('musteri')
			];
			$mevcut = (int) postvalue('mevcut_bakiye');
			$eklencek = (int) postvalue('eklenecek_bakiye');
			$promosyon = (int) postvalue('promosyon');
			if ($promosyon != 0) 
			{
				$baslangicTarihi = date('Y-m-d H:i:s');
				$promos = 
				[
					'kart_id'=>postvalue('musteri'),
					'islemtipi'=>2,
					'kullanildimi'=>1,
					'personel_id' =>postvalue('personel'),
					'date_time' => date('Y-m-d H:i:s', strtotime($baslangicTarihi) + 5),
					'date_' => date('Y-m-d'),
					'mevcut_dongu_baslangic' => $mevcut_dongu_baslangic,
					'eklenecek_bakiye' =>  $promosyon
				];
				bakiye::insert($promos);
			}
			$islem = $mevcut + $eklencek + $promosyon;
			$dizi = [
				'kullanildimi'=>1,
				'personel_id' =>postvalue('personel'),
				'date_time' => date('Y-m-d H:i:s'),
				'date_' => date('Y-m-d'),
				'mevcut_dongu_baslangic' => $mevcut_dongu_baslangic,
				'eklenecek_bakiye' =>  $eklencek
			];
			musteri::update(['kart_id'=>postvalue('musteri')],['bakiye'=>$islem]);
			$cloneyedek = [
				'kart_id'=>postvalue('musteri'),
				'bakiye' => $islem,
				'islem' => 'update'
			];
			mustericlone::insert($cloneyedek);
			bakiye::update($update,$dizi);
			back();
		}
	}
	public function barkodolustur()
	{
		if (isPost()) 
		{
			$personel = $this->session->admininfo->name;
			$ucretler = ucretler::select();
			while (True) {
				$ilkHarf = 'E';
				$randomPart = substr(uniqid(), 0, 12); 
				$barkodnumber = $ilkHarf . $randomPart;
				$kontrol = musteri::select(['kart_id'=>$barkodnumber]);
				if ($kontrol) {
					$ilkHarf = 'E';
					$randomPart = substr(uniqid(), 0, 12); 
					$barkodnumber = $ilkHarf . $randomPart;
				}else {
					break;
				}
			}
			$deneme = postvalue('tip');
			if (postvalue('tip') == 'yikama') 
			{
				$ucret = $ucretler[0]->yikama;
			}elseif(postvalue('tip') == 'kopuk')
			{
				$ucret = $ucretler[0]->kopuk;
			}
			elseif(postvalue('tip') == 'supurge')
			{
				$ucret = $ucretler[0]->supurge;
			}
			elseif(postvalue('tip') == 'hava')
			{
				$ucret = $ucretler[0]->hava;
			}
			$id = 1;
			$saat = dongubaslangic::find($id);
			$mevcut_dongu_baslangic = $saat->saat;
			$data=
			[
				'kart_id' 	=> $barkodnumber,
				'name' 		=> 'barkod',
				'telefon'	=> '0',
				'bakiye'	=> $ucret,
				'barkod_personel' => postvalue('tip')
			];
			$dizi = 
			[
				'kart_id' 	=> $barkodnumber,
				'personel_id' =>$personel,
				'kullanildimi'=>1,
				'eklenecek_bakiye' =>$ucret,
				'date_time' => date('Y-m-d H:i:s'),
				'date_' => date('Y-m-d'),
				'mevcut_dongu_baslangic' => $mevcut_dongu_baslangic
			];
			musteri::insert($data);
			bakiye::insert($dizi);
			flash('success','check','Başarılı','Barkod Başarıyla Oluşturuldu');
			redirect('admin/barkodyazdir/'.$barkodnumber);
		}


		$data['head'] = "Barkod Oluşturma Sayfası";
		if ($this->session->admininfo->barkodolustur_yetki=='1') 
		{
			$this->load->view('admin/pages/barkodolustur',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Barkod Oluşturma Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}
	}
	public function barkodyazdir($id)
	{
		$data['bilgi']=musteri::select(['kart_id'=>$id]);
		$data['barkode']= $id;
		$data['head'] = "Barkod Yazdırma Ekranı";
		$this->load->view('admin/pages/barkodyazdir',$data);
	}
	public function bakiyegetir()
	{
		$bakiye=bakiye::select(['kullanildimi'=>0]);

		if ($bakiye) {
			$mevcut=musteri::select(['kart_id'=>$bakiye[0]->kart_id]);
			$depozitoget = kiade::select(['kart_id'=>$bakiye[0]->kart_id]);
			$data=
			[
				'kart_id'=>$bakiye[0]->kart_id,
				'musteriadi'=>$mevcut[0]->name,
				'mevcut_bakiye'=>$mevcut[0]->bakiye,
				'depozito' =>$depozitoget[0]->depozito,
				'id'=>$bakiye[0]->id
			];
			echo json_encode($data);
		}else
		{
			$data=
			[
				'kart_id'=>'Lütfen Kart Okutunuz',
				'musteriadi'=>'---',
				'mevcut_bakiye'=>'0',
				'depozito'=>'0',
				'id'=>'0'
			];
			echo json_encode($data);
		}
	}
	public function musteriekle()
	{
		$bakiye=bakiye::select(['kullanildimi'=>3]);

		if ($bakiye) {
			$data=
			[
				'kart_id'=>$bakiye[0]->kart_id
			];
			echo json_encode($data);
		}else
		{
			$data=
			[
				'kart_id'=>'Lütfen Kart Okutunuz'
			];
			echo json_encode($data);
		}
	}
	public function musteriAddDatabase()
	{
		if (isPost()) 
		{
			$id = 1;
			$saat = dongubaslangic::find($id);
			$mevcut_dongu_baslangic = $saat->saat;
			$musteri = postvalue('musteri');
			$musteriadi = postvalue('musteriadi');
			$seflimusteri = sef($musteriadi);
			$plaka = postvalue('plaka');
			$telefon = postvalue('musteri_numarasi');
			$data=
			[
				'kart_id'=>postvalue('musteri'),
				'name'=>$seflimusteri,
				'plaka'=>postvalue('plaka'),
				'telefon'=>postvalue('musteri_numarasi'),
				'bakiye'=>0
			];
			$where=
			[
				'kart_id'=>postvalue('musteri'),
				'kullanildimi'=>3
			];
			$update=
			[
				'kullanildimi'=>1
			];
			$depozito=
			[
				'kart_id'=>postvalue('musteri'),
				'depozito'=>postvalue('depozito')
			];
			$vardiyadepozito=
			[
				'kart_id'=>postvalue('musteri'),
				'personel_id'=>postvalue('personel'),
				'depozito'=>postvalue('depozito'),
				'date_time'=> date('Y-m-d H:i:s'),
				'mevcut_dongu_baslangic'=>$mevcut_dongu_baslangic,
				'date_'=>date('Y-m-d')
			];
			vardiyadepozito::insert($vardiyadepozito);
			kiade::insert($depozito);
			bakiye::delete($where);
			musteri::insert($data);
			$cloneyedek = [
				'kart_id'=>postvalue('musteri'),
				'name'=>$seflimusteri,
				'plaka'=>postvalue('plaka'),
				'telefon'=>postvalue('musteri_numarasi'),
				'bakiye'=>0,
				'islem'=> 'insert'
			];
			mustericlone::insert($cloneyedek);
			flash('success','check','Başarılı','Müşteri Başarıyla Eklendi');
			redirect('admin/bakiyeyukle');
		}
	}

	public function musteriekleview()
	{
		$data['head']="Müşteri Kayıt Ekranı";
		$data['fiyatlar']=ucretler::find(['id'=>1]);
		if ($this->session->admininfo->musteriekle_yetki=='1') 
		{
			$this->load->view('admin/pages/musteriekle',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Müşteri Ekleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}		
	public function bakiyeyukle()
	{
		$data['head']="Bakiye Yükleme Ekranı";
		if ($this->session->admininfo->bakiyeyukle_yetki=='1') 
		{
			$this->load->view('admin/pages/bakiyeyukle',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Bakiye Yükleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function loggoruntule()
	{
		$data['loglar']=loglar::query('SELECT * FROM loglar WHERE kart_id NOT IN (35214629)');
		$data['head']="Log Kayıtları Görüntüleme Ekranı";
		if ($this->session->admininfo->loglarigoruntule_yetki == '1') 
		{
			$this->load->view('admin/pages/loglar',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Logları Görüntüleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function fiyatlar()
	{
		if (isPost()) 
		{
			$data=
			[
				'yikama' 	=>postvalue('su'),
				'kopuk'  	=>postvalue('kopuk'),
				'supurge'	=>postvalue('supurge'),
				'hava' 	 	=>postvalue('hava'),
				'depozito'	=>postvalue('depozito'),
				'date'   	=>date('d.m.Y')
			];
			ucretler::update(['id'=>1],$data);
			flash('success','check','Başarılı','Ücretler Başarıyla Güncelleştirildi');
			back();
		}
		$data['fiyatlar']=ucretler::find(['id'=>1]);
		$data['head']="Fiyat Düzenleme Ekranı";
		if ($this->session->admininfo->fiyatlar_yetki=='1') 
		{
			$this->load->view('admin/pages/fiyatlar',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Fiyatlar Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function sureler()
	{
		if (isPost()) 
		{
			$data=
			[
				'yikamasuresi' =>postvalue('su'),
				'kopuksuresi'  =>postvalue('kopuk'),
				'supurge' 	   =>postvalue('supurge'),
				'hava'		   =>postvalue('hava'),
				'tarih'   	   =>date('d.m.Y')
			];
			sureler::update(['id'=>1],$data);
			flash('success','check','Başarılı','Ücretler Başarıyla Güncelleştirildi');
			back();
		}
		$data['sureler']=sureler::find(['id'=>1]);
		$data['head']="Süre Düzenleme Ekranı";
		if ($this->session->admininfo->sureler_yetki=='1') 
		{
			$this->load->view('admin/pages/sureler',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Süreler Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function raporlar()
	{
		$data["yikamaharcanan"]=loglar::query("SELECT SUM(eski_bakiye) - SUM(yeni_bakiye) AS 'yikama' FROM loglar WHERE P=1");
		$data["kopukharcanan"]=loglar::query("SELECT SUM(eski_bakiye) - SUM(yeni_bakiye) AS 'kopuk' FROM loglar WHERE P=2");
		$data["depozitoguncel"]=kiade::query("SELECT SUM(depozito) AS 'depozitoguncel' FROM iade");
		$data["depozitodahilgeneltoplam"]=musteri::query("SELECT SUM(m.bakiye) + SUM(i.depozito) AS 'bakiye' FROM musteri m INNER JOIN iade i ON m.kart_id = i.kart_id");
		$data["depozitosuzgeneltoplam"]=musteri::query("SELECT SUM(bakiye) AS 'bakiye' FROM musteri WHERE kart_id NOT IN (831475652) AND kart_id NOT IN (35214629);");
		$data['head']="Rapor Ekranı";
		if ($this->session->admininfo->raporlar_yetki=='1') 
		{
			$this->load->view('admin/pages/raporlar',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Raporlar Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function kartiade()
	{
		$data['musteriler']=musteri::select(['name<>'=>'barkod']);
		$data['head']="Kart İade Ekranı";
		if ($this->session->admininfo->kartiade_yetki=='1') 
		{
			$this->load->view('admin/pages/kartiade',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Kart İade Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function iadebakiyecek()
	{
		if (isPost()) 
		{
			$id = postvalue('kart_id');
			$data=musteri::select(['kart_id'=>$id]);
			$depozito=kiade::select(['kart_id'=>$id]);
			if ($depozito) 
			{
				$depo = $depozito[0]->depozito;
			}else
			{
				$depo = 0;
			}
			if ($data) 
			{
				$value=
				[
					'bakiye'=>$data[0]->bakiye,
					'depozito'=>$depo
				];
				echo json_encode($value);
			}else
			{             
				$value=
				[
					'bakiye'=>0,
					'depozito'=>$depo
				];
				echo json_encode($value);
			}
		}
	}
	public function iptal()
	{
		redirect('admin/panel');
	}
	public function kartiadeprocess()
	{
		if (isPost()) 
		{
			$id = 1;
			$saat = dongubaslangic::find($id);
			$mevcut_dongu_baslangic = $saat->saat;
			$kart = postvalue('musteri');
			$depozitowhere=
			[
				'kart_id' => postvalue('musteri')
			];
			$depozito=
			[
				'personel' => postvalue('personel'),
				'iade_bakiye'=>postvalue('musteri_bakiyesi'),
				'depozito' =>0,
				'date_time' => date('Y-m-d H:i:s'),
				'date_' => date('Y-m-d'),
				'mevcut_dongu_baslangic' => $mevcut_dongu_baslangic,
				'iade_depozito'=>postvalue('depozito')
			];
			kiade::update($depozitowhere,$depozito);
			musteri::delete($depozitowhere);
			$cloneyedek = [
				'kart_id'=> $kart,
				'islem' => 'delete'
			];
			mustericlone::insert($cloneyedek);
			flash('success','check','Başarılı','Kart İadesi Başarıyla Gerçekleştirildi');
			back();
		}
	}
	public function kartiadeprocess2()
	{
		if (isPost()) 
		{
			$id = 1;
			$saat = dongubaslangic::find($id);
			$mevcut_dongu_baslangic = $saat->saat;
			$musteri = postvalue('musteri');
			$personel = postvalue('personel');
			$musteribakiye = postvalue('musteri_bakiyesi');
			$dpztucrt = postvalue('depozito');
			$bakiyeyuklemeislem=
			[
				'kart_id'=>$musteri,
				'kullanildimi'=>0
			];
			$depozitowhere=
			[
				'kart_id' => $musteri
			];
			$depozito=
			[
				'personel' => $personel,
				'iade_bakiye'=>$musteribakiye,
				'depozito' =>0,
				'date_time' => date('Y-m-d H:i:s'),
				'date_' => date('Y-m-d'),
				'mevcut_dongu_baslangic' => $mevcut_dongu_baslangic,
				'iade_depozito'=>$dpztucrt
			];
			kiade::update($depozitowhere,$depozito);
			bakiye::delete($bakiyeyuklemeislem);
			musteri::delete($depozitowhere);
			$cloneyedek = [
				'kart_id'=> $kart,
				'islem' => 'delete'
			];
			mustericlone::insert($cloneyedek);
			flash('success','check','Başarılı','Kart İadesi Başarıyla Gerçekleştirildi');
			back();
		}
	}
	public function barkodgoruntule()
	{
		$data['musteriler']=musteri::select(['name'=>'barkod']);
		$data['head']="Barkod Görüntüleme Ekranı";
		if ($this->session->admininfo->barkodlarigoruntule_yetki=='1') 
		{
			$this->load->view('admin/pages/barkodgoruntule',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Barkodları Görüntüleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function personelekle()
	{
		if (isPost()) {
			if (postvalue('user_type') == "Admin") {
				$yetki = 1;
				$data = [
					'musteriekle_yetki' => $yetki,
					'musterigoruntule_yetki' => $yetki,
					'bakiyeyukle_yetki' => $yetki,
					'barkodolustur_yetki' => $yetki,
					'barkodlarigoruntule_yetki' => $yetki,
					'loglarigoruntule_yetki' => $yetki,
					'fiyatlar_yetki' => $yetki,
					'sureler_yetki' => $yetki,
					'raporlar_yetki' => $yetki,
					'kartiade_yetki' => $yetki,
					'personelekle_yetki' => $yetki,
					'personelgoruntule_yetki' => $yetki,
					'musteriduzenle_yetki' => $yetki,
					'name' 		=> 		postvalue('name'),
					'password'  =>		postvalue('password'),
					'email' 	=>		postvalue('email'),
					'role' 		=>  	postvalue('user_type')

				];
			}elseif(postvalue('user_type') == "Personel")
			{
				$yetki = 0;
				$data = [
					'musteriekle_yetki' => 1,
					'musterigoruntule_yetki' => $yetki,
					'bakiyeyukle_yetki' => 1,
					'barkodolustur_yetki' => 1,
					'barkodlarigoruntule_yetki' => 1,
					'loglarigoruntule_yetki' => 1,
					'fiyatlar_yetki' => $yetki,
					'sureler_yetki' => $yetki,
					'raporlar_yetki' => $yetki,
					'kartiade_yetki' => 1,
					'personelekle_yetki' => $yetki,
					'personelgoruntule_yetki' => $yetki,
					'musteriduzenle_yetki' => $yetki,
					'name' 		=> 		postvalue('name'),
					'password'  =>		postvalue('password'),
					'email' 	=>		postvalue('email'),
					'role' 		=>  	postvalue('user_type')

				];
			}
			Users::insert($data);
		}

		$data['head']="Personel Ekleme Ekranı";
		if ($this->session->admininfo->personelekle_yetki=='1') 
		{
			$this->load->view('admin/pages/personelekle',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Personel Ekleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}

	}
	public function personelgoruntule()
	{
		$data['personeller']=Users::select();
		$data['head']="Personel Görüntüleme Ekranı";
		if ($this->session->admininfo->personelgoruntule_yetki=='1') 
		{
			$this->load->view('admin/pages/personelgoruntule',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','Personel Görüntüleme Sayfasına erişim yetkiniz bulunmamaktadır.');
			back();
		}
	}
	public function personelsil($id)
	{
		$isExist=Users::find($id);
		if($isExist)
		{
			$data['is_active']= 0;
			Users::update($id,$data);
			flash('success','check','Başarılı','Personel Başarıyla Silindi');
			back();
		}
	}
	public function yetkiler()
	{
		$data['user']=$user=postvalue('user_name');
		$data['head']="Yetki Ayarlama Sayfası";
		$data['kullanici']=Users::find($user);
		if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") 
		{
			$this->load->view('admin/pages/yetkiler',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','YETKI YOK.');
			back();
		}

	}
	public function yetkilerview()
	{
		$data['users'] =Users::select();
		if ($this->session->admininfo->role=="Admin" OR $this->session->admininfo->role=="Supervisor") 
		{
			$data['head']="Yetki Ayarlama Sayfası";
			$this->load->view('admin/pages/yetkiduzenle',$data);
		}else
		{
			flash('danger','window-close','ERİŞİM REDDEDİLDİ !!!','YETKI YOK.');
			back();
		}

	}
	public function yetkiguncelle($id)
	{
		if (isset($_POST['kaydet'])) 
		{
			$data=[
				'musteriekle_yetki' =>postvalue('musteriekle_yetki') ,
				'musterigoruntule_yetki' => postvalue('musterigoruntule_yetki'),
				'bakiyeyukle_yetki' => postvalue('bakiyeyukle_yetki'),
				'barkodolustur_yetki' => postvalue('barkodolustur_yetki'),
				'barkodlarigoruntule_yetki' => postvalue('barkodlarigoruntule_yetki'),
				'loglarigoruntule_yetki' => postvalue('loglarigoruntule_yetki'),
				'fiyatlar_yetki' => postvalue('fiyatlar_yetki'),
				'sureler_yetki' => postvalue('sureler_yetki'),
				'raporlar_yetki' => postvalue('raporlar_yetki'),
				'kartiade_yetki' => postvalue('kartiade_yetki'),
				'personelekle_yetki' => postvalue('personelekle_yetki'),
				'personelgoruntule_yetki' => postvalue('personelgoruntule_yetki'),

			];

		}
		if (isset($_POST['tumyetkileriver'])) {
			$yetki = 1;
			$data= [
				'musteriekle_yetki' => $yetki,
				'musterigoruntule_yetki' => $yetki,
				'bakiyeyukle_yetki' => $yetki,
				'barkodolustur_yetki' => $yetki,
				'barkodlarigoruntule_yetki' => $yetki,
				'loglarigoruntule_yetki' => $yetki,
				'fiyatlar_yetki' => $yetki,
				'sureler_yetki' => $yetki,
				'raporlar_yetki' => $yetki,
				'kartiade_yetki' => $yetki,
				'personelekle_yetki' => $yetki,
				'personelgoruntule_yetki' => $yetki,
				'musteriduzenle_yetki' => $yetki
			];

		}
		if (isset($_POST['tumyetkilerial'])) {
			$yetki= 0 ;
			$data= [
				'musteriekle_yetki' => $yetki,
				'musterigoruntule_yetki' => $yetki,
				'bakiyeyukle_yetki' => $yetki,
				'barkodolustur_yetki' => $yetki,
				'barkodlarigoruntule_yetki' => $yetki,
				'loglarigoruntule_yetki' => $yetki,
				'fiyatlar_yetki' => $yetki,
				'sureler_yetki' => $yetki,
				'raporlar_yetki' => $yetki,
				'kartiade_yetki' => $yetki,
				'personelekle_yetki' => $yetki,
				'personelgoruntule_yetki' => $yetki,
				'musteriduzenle_yetki' => $yetki
			];
		}
		Users::update($id,$data);
		flash('success','check','Başarılı','Kullanıcı Yetkileri Güncellendi.');
		redirect('admin/yetkilerview');
	}
	public function promosyonekle()
	{
		if (isPost()) 
		{
			$data=
			[
				'yukleme_miktari' => postvalue('yukleme'),
				'hediye_yikama'	=> postvalue('yikama'),
				'hediye_kopuk'	=> postvalue('kopuk'),
				'date' => date('d.m.Y')
			];
			promosyonlar::insert($data);
			flash('success','check','Başarılı','Promosyon Başarıyla Eklendi.');
			back();
		}
		$data['head']="Promosyon Ekleme Ekranı";
		$this->load->view('admin/pages/promosyonekle',$data);
	}
	public function promosyonlar()
	{
		$data['promosyonlar']=promosyonlar::select();
		$data['head']="Promosyonlar Ekranı";
		$this->load->view('admin/pages/promosyonlar',$data);
	}
	public function promosyonsil($id)
	{
		$isExist = promosyonlar::find(['id'=>$id]);
		if ($isExist) 
		{
			promosyonlar::delete(['id'=>$id]);
			flash('success','check','Başarılı','Promosyon Başarıyla Kaldırıldı.');
			back();
		}
	}
	public function promosyonprocess($id)
	{
		$result = promosyonlar::query("SELECT yukleme_miktari AS 'yukleme',hediye_yikama AS 'yikama',hediye_kopuk AS 'kopuk' FROM promosyonlar WHERE yukleme_miktari <= '$id' ORDER BY yukleme_miktari DESC LIMIT 0,1");
		$ucretler = ucretler::select();
		$yikamaucreti = $result[0]->yikama * $ucretler[0]->yikama;
		$kopukucreti = $result[0]->kopuk * $ucretler[0]->kopuk;
		$return = 
		[
			'rakam' => $yikamaucreti + $kopukucreti,
			'yikama' => $result[0]->yikama,
			'kopuk' => $result[0]->kopuk
		];
		echo json_encode($return);

	}
	public function barkodsorgu()
	{
		if (isPost()) 
		{
			$barkodbilgisi = postvalue('barkod');
			$barkod=musteri::find(['kart_id'=>$barkodbilgisi]);
			if ($barkod) 
			{
				$data['head']="Barkod Görüntüleme Ekranı";
				$data['musteriler']=musteri::select(['kart_id'=>$barkodbilgisi]);
				$this->load->view('admin/pages/barkodsorguekrani',$data);
			}
			else
			{
				flash('danger','window-close','HATA !!!','Sorguladığınız barkod bulunmamaktadır.');
				back();
			}
		}else
		{
			$data['head']="Barkod Sorgu Ekranı";
			$this->load->view('admin/pages/barkodsorgu',$data);
		}
	}


}
