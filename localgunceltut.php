<?php 

//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BASLANGIC
$dosyaYolu = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

if (file_exists($dosyaYolu)) {
	if (unlink($dosyaYolu)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}

//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BITIS

//*****************************************************************************************************

//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BASLANGIC
$host = "***********";
$db_user = "u401251138_test";
$db_pass = "J$2wJwki";
$backup_dir = "C:/xampp/htdocs/petrolsistemi/yedekler/";

$db_names = "u401251138_test";
$table_name = "musteri";
$backup_file = "musteri_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name} > {$backup_file}");
//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BITIS

//*****************************************************************************************************

//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BASLANGIC
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "petrolsistemi";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
	die("Bağlantı hatası: " . $conn->connect_error);
}

$sqlFile = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

$sql = file_get_contents($sqlFile);

$queries = explode(';', $sql);

foreach ($queries as $query) {
	$query = trim($query);
	if (!empty($query)) {
		$result = $conn->query($query);
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}

echo "SQL dosyası başarıyla içe aktarıldı!";

$conn->close();
//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BITIS

//*****************************************************************************************************

//BIR ONCEKI YEDEKLENMIS DOSYAYI SILME FUNCTION -- BASLANGIC
$dosyaYolu = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

if (file_exists($dosyaYolu)) {
	if (unlink($dosyaYolu)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}














?>