<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<!--<PERSON><PERSON><PERSON> b<PERSON>şlangıcı -->
<section class="content">
  <div class="row">
    <div class="col-md-3"></div>
    <div class="col-md-6">
      <form action="<?php echo base_url('rapor/musteriduzenlee'); ?>" role="form" method="POST" enctype= "multipart/form-data">
        <div class="card card-primary">
          <div class="card-body">
           <div class="form-group">
            <label for="inputName">Müşteri İsim ve Soyisim</label>
            <input type="text" name="name" class="form-control"  value="<?php echo $musteri->name ; ?>"> 
          </div>
          <div class="form-group">
            <label for="inputName">Müşteri <PERSON></label>
            <input type="text" name="plaka" class="form-control"  value="<?php echo $musteri->plaka ; ?>"> 
          </div>
          <input type="hidden" name="id" value="<?php echo $musteri->id; ?>">
          <div class="form-group">
            <label for="inputName">Müşteri Telefon Numarası</label>
            <input type="text"name="telefon" class="form-control"  value="<?php echo $musteri->telefon ; ?>">
          </div>
          <div class="form-group">
            <label for="inputName">Mevcut Bakiye</label>
            <input type="text"name="bakiye" class="form-control" value="<?php echo $musteri->bakiye ; ?>">
          </div>
        </div>
        <!-- /.card-body -->
      </div>
      <!-- /.card -->
    </div>
  </div>
  <div class="row">
    <div class="col-md-3"></div>
    <div class="col-6">
      <button type="submit"  class="btn btn-success float-right">Güncelle</button>
    </div>
  </div>
</form>
</section>
<!-- /.content -->
<script type="text/javascript" src="<?php echo base_url('js/custom2.js') ?>"> </script>
<?php $this->load->view('admin/include/footer'); ?>