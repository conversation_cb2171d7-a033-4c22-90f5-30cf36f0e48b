<?php 
$target_host = '***********';
$target_user = 'u411458722_pekdemir';
$target_pass = '?7cy+18Z6:x';
$target_db = 'u411458722_pekdemir';
//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BITIS

//*****************************************************************************************************

$online_conn = new mysqli($target_host, $target_user, $target_pass, $target_db);

if ($online_conn->connect_error) {
	die("Online veritabanına bağlanma hatası: " . $online_conn->connect_error);
}

$checkTableQuery = "SELECT COUNT(*) as rowCount FROM mustericlone";
$result = $online_conn->query($checkTableQuery);

if ($result) {
	$row = $result->fetch_assoc();
	$rowCount = $row["rowCount"];

	if ($rowCount > 0) {
        // mustericlone tablosunda veri var, işlemi yapma
        //echo "Online mustericlone tablosunda zaten veri var, işlem yapılmayacak.";
	} else {
		$servername = "localhost";
		$username = "root";
		$password = "";
		$dbname = "petrolsistemi";

		$conn = new mysqli($servername, $username, $password, $dbname);
		$checkTableQueryy = "SELECT COUNT(*) as rowCount FROM mustericlone";
		$resultt = $conn->query($checkTableQueryy);

		if ($resultt) {
			$roww = $resultt->fetch_assoc();
			$rowCountt = $roww["rowCount"];

			if ($rowCountt > 0) {
        		// mustericlone tablosunda veri var, işlemi yapma
        		//echo "Online mustericlone tablosunda zaten veri var, işlem yapılmayacak.";
			} else {
        //ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BASLANGIC
				$host = "***********";
				$db_user = "u411458722_pekdemir";
				$db_pass = "?7cy+18Z6:x";
				$backup_dir = "C:/xampp/htdocs/petrolsistemi/yedekler/";

				$db_names = "u411458722_pekdemir";
				$table_name = "musteri";
				$backup_file = $backup_dir . "musteri_backup.sql";
				exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name} > {$backup_file}");
//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BITIS

//*****************************************************************************************************

//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BASLANGIC
				

				if ($conn->connect_error) {
					die("Bağlantı hatası: " . $conn->connect_error);
				}

				$sqlFile = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

				$sql = file_get_contents($sqlFile);

				$queries = explode(';', $sql);

				foreach ($queries as $query) {
					$query = trim($query);
					if (!empty($query)) {
						$result = $conn->query($query);
						if (!$result) {
							echo "Hata: " . $conn->error;
						}
					}
				}

//echo "SQL dosyası başarıyla içe aktarıldı!";

				$conn->close();
//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BITIS

//*****************************************************************************************************

//BIR ONCEKI YEDEKLENMIS DOSYAYI SILME FUNCTION -- BASLANGIC
				$dosyaYolu = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

				if (file_exists($dosyaYolu)) {
					if (unlink($dosyaYolu)) {
		//echo "Dosya başarıyla silindi.";
					} else {
		//echo "Dosya silinemedi.";
					}
				} else {
	//echo "Dosya mevcut değil.";
				}
			}
		}
	} else {
		echo "Hata: " . $online_conn->error;
	}

	$online_conn->close();
	?>