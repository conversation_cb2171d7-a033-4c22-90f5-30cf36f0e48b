<?php 
defined('BASEPATH') OR exit('No direct script access allowed');
class Machine extends CI_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->targethost = array(
			'target_host' => '***********',
			'target_user' => 'u411458722_pekdemir',
			'target_pass' => '?7cy+18Z6:x',
			'target_db' => 'u411458722_pekdemir',
			'target_table' => 'musteri'
		);
		$this->sourcehost= array(
			'source_host' => 'localhost',
			'source_user' => 'root',
			'source_pass' => '',
			'source_db' => 'petrolsistemi'
		);
	}
		//Bakiye Yükleme Kutusu Fonksiyonu
	public function bakiyeyukle()
	{
		//set_time_limit(9);
		//for ($i=0; $i < 1; $i++) { 
		if (isPost()) {
			$id = $_POST['ID'];
			//$pid = $_POST['PID'];
			//$personel=users::select(['kart_id'=>$pid]);
			//$ad = $personel[0]->name;
			$musteriVarmi=musteri::select(['kart_id'=>$id]);
			if ($musteriVarmi) 
			{
				$sonuc=
				[
					'kart_id' =>$id,
					'kullanildimi'=>0
				];
				bakiye::insert($sonuc);
				if ($this->db->affected_rows() == 1) {
					echo "sa";
				} else {
    				// Hata mesajı gönderin ve işlemi durdurun
					die("Hata: Form verisi birden fazla kez gönderildi.");
				}
			}
			else
			{
				$sonuc=
				[
					'kart_id' =>$id,
					'kullanildimi'=>3
				];
				bakiye::insert($sonuc);
				if ($this->db->affected_rows() == 1) {
    				// Form verilerini işleyin
				} else {
    				// Hata mesajı gönderin ve işlemi durdurun
					die("Hata: Form verisi birden fazla kez gönderildi.");
				}
			}
		}
	//	}
	}
	public function server()
	{
		if (isPost()) 
		{
			$ID = $_POST['ID'];
			$P = $_POST['P'];
			$ONO = $_POST['ONO'];
			$exit = $_POST['EXIT'];
			$ucret = ucretler::query("SELECT * FROM ucretler ORDER BY id DESC LIMIT 1");
			$musteri = musteri::select(['kart_id'=>$ID]);
			$bakiye =$musteri[0]->bakiye;
			$barkod = $musteri[0]->barkod_personel;
			if ($exit == 1) 
			{
				if ($barkod) 
				{
					if ($barkod == 'yikama') 
					{
						if ($P == 1) 
						{
							$bakiyekontrol = $ucret[0]->yikama;
							if ($bakiye >= $bakiyekontrol) 
							{	
								$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
								$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
								$surebilgisi = $sure[0]->yikamasuresi;
								$baseTime = time();
								list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
								$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
								$result = date('d.m.Y H:i:s', $newTime);
								$data=
								[
									'kart_id' =>$ID,
									'calistimi'=>1,
									'P' =>$P,
									'sure' =>$surebilgisi,
									'eski_bakiye'=>$bakiye,
									'yeni_bakiye'=>$kalanbakiye,
									'okuyucu_numarasi'=>$ONO,
									'dakikasaniye'=>$result,
									'date'=>date('d.m.Y H:i:s')
								];
								$update = 
								[
									'bakiye' => $kalanbakiye
								];
								musteri::update(['kart_id'=>$ID],$update);
								loglar::insert($data);
								echo "*T:$surebilgisi:$kalanbakiye*";
								$cloneyedek = [
									'kart_id'=> $ID,
									'bakiye' => $kalanbakiye,
									'islem' => 'update'
								];
								mustericlone::insert($cloneyedek);
							}else
							{
								echo "*F:00:00:$bakiye*";
							}
						}
						else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
					elseif($barkod == 'kopuk')
					{
						if ($P == 2) 
						{
							$bakiyekontrol = $ucret[0]->kopuk;
							if ($bakiye >= $bakiyekontrol) 
							{	
								$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
								$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
								$surebilgisi = $sure[0]->kopuksuresi;
								$baseTime = time();
								list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
								$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
								$result = date('d.m.Y H:i:s', $newTime);
								$data=
								[
									'kart_id' =>$ID,
									'calistimi'=>1,
									'P' =>$P,
									'sure' =>$surebilgisi,
									'eski_bakiye'=>$bakiye,
									'yeni_bakiye'=>$kalanbakiye,
									'okuyucu_numarasi'=>$ONO,
									'dakikasaniye'=>$result,
									'date'=>date('d.m.Y H:i:s')
								];
								$update = 
								[
									'bakiye' => $kalanbakiye
								];
								musteri::update(['kart_id'=>$ID],$update);
								loglar::insert($data);
								echo "*T:$surebilgisi:$kalanbakiye*";
								$cloneyedek = [
									'kart_id'=> $ID,
									'bakiye' => $kalanbakiye,
									'islem' => 'update'
								];
								mustericlone::insert($cloneyedek);
								
							}else
							{
								echo "*F:00:00:$bakiye*";
							}
						}
						else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
					elseif ($barkod == 'supurge')
					{
						if ($P == 3) 
						{
							$bakiyekontrol = $ucret[0]->supurge;
							if ($bakiye >= $bakiyekontrol) 
							{	
								$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
								$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
								$surebilgisi = $sure[0]->supurge;
								$baseTime = time();
								list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
								$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
								$result = date('d.m.Y H:i:s', $newTime);
								$data=
								[
									'kart_id' =>$ID,
									'calistimi'=>1,
									'P' =>$P,
									'sure' =>$surebilgisi,
									'eski_bakiye'=>$bakiye,
									'yeni_bakiye'=>$kalanbakiye,
									'okuyucu_numarasi'=>$ONO,
									'dakikasaniye'=>$result,
									'date'=>date('d.m.Y H:i:s')
								];
								$update = 
								[
									'bakiye' => $kalanbakiye
								];
								musteri::update(['kart_id'=>$ID],$update);
								loglar::insert($data);
								echo "*T:$surebilgisi:$kalanbakiye*";
								$cloneyedek = [
									'kart_id'=> $ID,
									'bakiye' => $kalanbakiye,
									'islem' => 'update'
								];
								mustericlone::insert($cloneyedek);
							}else
							{
								echo "*F:00:00:$bakiye*";
							}
						}
						else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
					elseif($barkod == 'hava')
					{
						if ($P == 4) 
						{
							$bakiyekontrol = $ucret[0]->hava;
							if ($bakiye >= $bakiyekontrol) 
							{	
								$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
								$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
								$surebilgisi = $sure[0]->hava;
								$baseTime = time();
								list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
								$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
								$result = date('d.m.Y H:i:s', $newTime);
								$data=
								[
									'kart_id' =>$ID,
									'calistimi'=>1,
									'P' =>$P,
									'sure' =>$surebilgisi,
									'eski_bakiye'=>$bakiye,
									'yeni_bakiye'=>$kalanbakiye,
									'okuyucu_numarasi'=>$ONO,
									'dakikasaniye'=>$result,
									'date'=>date('d.m.Y H:i:s')
								];
								$update = 
								[
									'bakiye' => $kalanbakiye
								];
								musteri::update(['kart_id'=>$ID],$update);
								loglar::insert($data);
								echo "*T:$surebilgisi:$kalanbakiye*";
								$cloneyedek = [
									'kart_id'=> $ID,
									'bakiye' => $kalanbakiye,
									'islem' => 'update'
								];
								mustericlone::insert($cloneyedek);
								
							}else
							{
								echo "*F:00:00:$bakiye*";
							}
						}
						else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
				}else
				{
					if ($P == 1) 
					{
						$bakiyekontrol = $ucret[0]->yikama;
						if ($bakiye >= $bakiyekontrol) 
						{	
							$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
							$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
							$surebilgisi = $sure[0]->yikamasuresi;
							$baseTime = time();
							list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
							$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
							$result = date('d.m.Y H:i:s', $newTime);
							$data=
							[
								'kart_id' =>$ID,
								'calistimi'=>1,
								'P' =>$P,
								'sure' =>$surebilgisi,
								'eski_bakiye'=>$bakiye,
								'yeni_bakiye'=>$kalanbakiye,
								'okuyucu_numarasi'=>$ONO,
								'dakikasaniye'=>$result,
								'date'=>date('d.m.Y H:i:s')
							];
							$update = 
							[
								'bakiye' => $kalanbakiye
							];
							musteri::update(['kart_id'=>$ID],$update);
							loglar::insert($data);
							echo "*T:$surebilgisi:$kalanbakiye*";
							$cloneyedek = [
								'kart_id'=> $ID,
								'bakiye' => $kalanbakiye,
								'islem' => 'update'
							];
							mustericlone::insert($cloneyedek);
							
						}else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
				//P == 2 
					else if ($P == 2) 
					{
						$bakiyekontrol = $ucret[0]->kopuk;
						if ($bakiye >= $bakiyekontrol) 
						{
							$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
							$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
							$surebilgisi = $sure[0]->kopuksuresi;
							$baseTime = time();
							list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
							$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
							$result = date('d.m.Y H:i:s', $newTime);
							$data=
							[
								'kart_id' =>$ID,
								'calistimi'=>1,
								'P' =>$P,
								'sure' =>$surebilgisi,
								'eski_bakiye'=>$bakiye,
								'yeni_bakiye'=>$kalanbakiye,
								'okuyucu_numarasi'=>$ONO,
								'dakikasaniye'=>$result,
								'date'=>date('d.m.Y H:i:s')
							];
							$update = 
							[
								'bakiye' => $kalanbakiye
							];
							musteri::update(['kart_id'=>$ID],$update);
							loglar::insert($data);
							echo "*T:$surebilgisi:$kalanbakiye*";
							$cloneyedek = [
								'kart_id'=> $ID,
								'bakiye' => $kalanbakiye,
								'islem' => 'update'
							];
							mustericlone::insert($cloneyedek);
							
						}else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
					else if ($P == 3) 
					{
						$bakiyekontrol = $ucret[0]->supurge;
						if ($bakiye >= $bakiyekontrol) 
						{
							$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
							$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
							$surebilgisi = $sure[0]->supurge;
							$baseTime = time();
							list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
							$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
							$result = date('d.m.Y H:i:s', $newTime);
							$data=
							[
								'kart_id' =>$ID,
								'calistimi'=>1,
								'P' =>$P,
								'sure' =>$surebilgisi,
								'eski_bakiye'=>$bakiye,
								'yeni_bakiye'=>$kalanbakiye,
								'okuyucu_numarasi'=>$ONO,
								'dakikasaniye'=>$result,
								'date'=>date('d.m.Y H:i:s')
							];
							$update = 
							[
								'bakiye' => $kalanbakiye
							];
							musteri::update(['kart_id'=>$ID],$update);
							loglar::insert($data);
							echo "*T:$surebilgisi:$kalanbakiye*";
							$cloneyedek = [
								'kart_id'=> $ID,
								'bakiye' => $kalanbakiye,
								'islem' => 'update'
							];
							mustericlone::insert($cloneyedek);
							
							
						}else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
					else  
					{
						$bakiyekontrol = $ucret[0]->hava;
						if ($bakiye >= $bakiyekontrol) 
						{
							$kalanbakiye = (int) $bakiye - (int) $bakiyekontrol;
							$sure = sureler::query("SELECT * FROM sureler ORDER BY id DESC LIMIT 1");
							$surebilgisi = $sure[0]->hava;
							$baseTime = time();
							list($minutesToAdd, $secondsToAdd) = explode(":", $surebilgisi);
							$newTime = $baseTime + ($minutesToAdd * 60) + $secondsToAdd;
							$result = date('d.m.Y H:i:s', $newTime);
							$data=
							[
								'kart_id' =>$ID,
								'calistimi'=>1,
								'P' =>$P,
								'sure' =>$surebilgisi,
								'eski_bakiye'=>$bakiye,
								'yeni_bakiye'=>$kalanbakiye,
								'okuyucu_numarasi'=>$ONO,
								'dakikasaniye'=>$result,
								'date'=>date('d.m.Y H:i:s')
							];
							$update = 
							[
								'bakiye' => $kalanbakiye
							];
							musteri::update(['kart_id'=>$ID],$update);
							loglar::insert($data);
							echo "*T:$surebilgisi:$kalanbakiye*";
							$cloneyedek = [
								'kart_id'=> $ID,
								'bakiye' => $kalanbakiye,
								'islem' => 'update'
							];
							mustericlone::insert($cloneyedek);
							
						}else
						{
							echo "*F:00:00:$bakiye*";
						}
					}
				}
			}
			//exit == 2
			else
			{
				$where=
				[
					'kart_id' => $ID,
					'calistimi'=>1,
					'P'=>$P,
					'okuyucu_numarasi'=>$ONO,
				];
				loglar::update($where,['calistimi'=>2]);
			}
		}
		// ispost
		else
		{
			echo "Veri Gelmiyor.";
		}
	}
}