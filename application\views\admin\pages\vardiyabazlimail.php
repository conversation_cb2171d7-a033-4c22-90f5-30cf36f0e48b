<?php

date_default_timezone_set('Europe/Istanbul');
$config=array(
	"protocol" =>"smtp",
	"smtp_host" =>"ssl://elkos.com.tr",
	"smtp_port" =>"465",
	"smtp_user" =>"<EMAIL>",
	"smtp_pass" => "Technolobal2022",
	"starttls" =>true,
	"charset"=> "utf-8",
	"mailtype" => "html",
	"wordwrap" =>true,
	"newline" => "\r\n",
);
$promosyonkartlari = "768290636";
$vardiya1toplam = 0;
$vardiya2toplam = 0;
$vardiya3toplam = 0;
$vardiya4toplam = 0;
$vardiya5toplam = 0;
$vardiya6toplam = 0;
$vardiyatoplam = 0;
$vardiya1promosyon = 0;
$vardiya2promosyon = 0;
$vardiya3promosyon = 0;
$vardiya4promosyon = 0;
$vardiya5promosyon = 0;
$vardiya6promosyon = 0;
$vardiyalarpromosyontoplam = 0;
$vardiya1bakiyeiadetoplam = 0;
$vardiya2bakiyeiadetoplam = 0;
$vardiya3bakiyeiadetoplam = 0;
$vardiya4bakiyeiadetoplam = 0;
$vardiya5bakiyeiadetoplam = 0;
$vardiya6bakiyeiadetoplam = 0;
$vardiyabakiyeiadetoplam = 0;
$vardiya1depozitoiadetoplam = 0;
$vardiya2depozitoiadetoplam = 0;
$vardiya3depozitoiadetoplam = 0;
$vardiya4depozitoiadetoplam = 0;
$vardiya5depozitoiadetoplam = 0;
$vardiya6depozitoiadetoplam = 0;
$vardiyadepozitoiadetoplam = 0;
$vardiya1depozitotoplam = 0;
$vardiya2depozitotoplam = 0;
$vardiya3depozitotoplam = 0;
$vardiya4depozitotoplam = 0;
$vardiya5depozitotoplam = 0;
$vardiya6depozitotoplam = 0;
$vardiyadepozitotoplam = 0;
$professorsnape = date("Y-m-d");
$professorsnape1 = new DateTimeImmutable($professorsnape);
$professorsnape2 = $professorsnape1->modify('-3 day');
$professorsnape3=$professorsnape2->format('Y-m-d');
$snape = $professorsnape3;
$datee = date("d.m.y H:i:s");
$raporlar_denetim = bakiye::query("SELECT * FROM bakiye_yukle WHERE date_ = '$snape' ");
		$severus_snapee=$raporlar_denetim[0]->mevcut_dongu_baslangic; //bravest man i have ever seen    
		if (!$severus_snapee) {
			$message = " $datee Raporlama Hatası (Muhtelemel bir önceki güne ait raporlarda bir problem veya eksiklik var...)";
		}else{
			$combinedDT = date('Y-m-d H:i:s', strtotime("$snape $severus_snapee"));
			$start = new DateTimeImmutable($combinedDT);
			$datetime = $start->modify('+1 day');
			$date_time=$datetime->format('Y-m-d H:i:s');
			$iade = bakiye::query("SELECT DISTINCT kart_id FROM bakiye_yukle WHERE date_time BETWEEN '$combinedDT' AND '$date_time' ");
			$toplam = 0;
			foreach ($iade as $iade) {
				$iade_deneme=kiade::query("SELECT * FROM iade WHERE kart_id = '$iade->kart_id' ");
				$toplam = $toplam + (int) $iade_deneme[0]->depozito;
			}
			$depozito_toplam = $toplam;
			$gorevalmiskisiler = bakiye::query("SELECT DISTINCT personel_id FROM bakiye_yukle WHERE date_time BETWEEN '$combinedDT' AND '$date_time' ");
			$counter = 0;
			foreach ($gorevalmiskisiler as $gorev) {
				$counter += 1;
			}
			if ($counter == 1) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$raporlar1 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor1 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor11 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor1 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$message="$datee
				<!DOCTYPE html>
				<html>
				<body>
				<style>
				table, th, td {
					border: 1px solid;
				}
				p {
					text-align: center;
				}
				hr {
					border: 7px solid red;
					border-radius: 5px;
				}
				</style>
				<b><label>1. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar1 as $vardiya1) 
				{ 
					$kart_id = $raporlar1->kart_id;
					$personel_id = $raporlar1->personel_id;
					$eklenecek_bakiye = $raporlar1->eklenecek_bakiye;
					$datee_time = $raporlar1->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>1. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor1 as $iaderapor1){
					$kart_id = $iaderapor1->kart_id;
					$personel = $iaderapor1->personel;
					$iade_bakiye = $iaderapor1->iade_bakiye;
					$iade_depozito = $iaderapor1->iade_depozito;
					$datee_time = $iaderapor1->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>
				<b><label>VARDİYALAR TOPLAMI</label></b>
				<div class='row'>
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>1.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>1.Vardiya Toplamı</td>
				";
				foreach ($raporlar1bakiye as $raporlar1bakiye) {
					$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya1toplam  ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor1 as $depozitorapor1) {
						$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
					}
				}
				$message .= "
				<td>$vardiya1depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>1. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar1promosyon) {

					foreach ($raporlar1promosyon as $raporlar1promosyon) {
						$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya1promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				<tr>
				<td>1.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor11) {

					foreach ($iaderapor11 as $iaderapor1) {
						$toplanacakbakiye = $iaderapor1->iade_bakiye;
						$toplanacakdepozito = $iaderapor1->iade_depozito;
						$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya1bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya1depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				<table  class='table table-bordered table-striped col-md-2' >
				<thead>
				<tr>
				<th colspan='2'><center>GENEL TOPLAM</center></th>
				</tr>
				</thead>
				<tbody>
				<tr>
				<td>Vardiyalar Genel Toplamı</td>
				";
				$vardiyatoplam = $vardiyatoplam + $vardiya1toplam;
				$message .= "
				<td>$vardiyatoplam ₺</td>
				</tr>
				<tr>
				<td>Depozitolar Genel Toplamı</td>
				";
				$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam;
				$message .= "
				<td>$vardiyadepozitotoplam ₺</td>
				</tr>
				<tr>
				<td>Promosyonlar Genel Toplamı</td>
				";
				$vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon;
				$message .= "
				<td>$vardiyalarpromosyontoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Bakiye İadeleri Genel Toplamı</td>
				";
				$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam;
				$message .= "
				<td>$vardiyabakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Depozito İadeleri Genel Toplamı</td>
				";
				$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam;
				$message .= "
				<td>$vardiyadepozitoiadetoplam ₺</td>
				</tr>
				</tbody>
				</table>
				</div>
				";
			}elseif ($counter == 2) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$raporlar1 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor1 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor11 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor1 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor2 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor22 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor2 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$message="$datee
				<!DOCTYPE html>
				<html>
				<body>
				<style>
				table, th, td {
					border: 1px solid;
				}
				p {
					text-align: center;
				}
				hr {
					border: 7px solid red;
					border-radius: 5px;
				}
				</style>
				<b><label>1. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar1 as $raporlar1) 
				{ 
					$kart_id = $raporlar1->kart_id;
					$personel_id = $raporlar1->personel_id;
					$eklenecek_bakiye = $raporlar1->eklenecek_bakiye;
					$datee_time = $raporlar1->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<br>
				<b><label>1. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor1 as $iaderapor1){
					$kart_id = $iaderapor1->kart_id;
					$personel = $iaderapor1->personel;
					$iade_bakiye = $iaderapor1->iade_bakiye;
					$iade_depozito = $iaderapor1->iade_depozito;
					$datee_time = $iaderapor1->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.= "
				<b><label>2. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar2 as $vardiya2) 
				{ 
					$kart_id = $vardiya2->kart_id;
					$personel_id = $vardiya2->personel_id;
					$eklenecek_bakiye = $vardiya2->eklenecek_bakiye;
					$datee_time = $vardiya2->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>2. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor2 as $iaderapor2){
					$kart_id = $iaderapor2->kart_id;
					$personel = $iaderapor2->personel;
					$iade_bakiye = $iaderapor2->iade_bakiye;
					$iade_depozito = $iaderapor2->iade_depozito;
					$datee_time = $iaderapor2->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				
				$message.="
				<b><label>VARDİYALAR TOPLAMI</label></b>
				<div class='row'>
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>1.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>1.Vardiya Toplamı</td>
				";
				foreach ($raporlar1bakiye as $raporlar1bakiye) {
					$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya1toplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor1 as $depozitorapor1) {
						$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
					}
				}
				$message .= "
				<td>$vardiya1depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>1. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar1promosyon) {

					foreach ($raporlar1promosyon as $raporlar1promosyon) {
						$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya1promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				<tr>
				<td>1.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor11) {

					foreach ($iaderapor11 as $iaderapor1) {
						$toplanacakbakiye = $iaderapor1->iade_bakiye;
						$toplanacakdepozito = $iaderapor1->iade_depozito;
						$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya1bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya1depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";

				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>2.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>2.Vardiya Toplamı</td>
				";
				foreach ($raporlar2bakiye as $raporlar2bakiye) {
					$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya2toplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor2 as $depozitorapor2) {
						$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
					}
				}
				$message .= "
				<td>$vardiya2depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>2. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar2promosyon) {

					foreach ($raporlar2promosyon as $raporlar2promosyon) {
						$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya2promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>2.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor22) {

					foreach ($iaderapor22 as $iaderapor2) {
						$toplanacakbakiye = $iaderapor2->iade_bakiye;
						$toplanacakdepozito = $iaderapor2->iade_depozito;
						$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya2bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya2depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";
				$message .= "
				<table  class='table table-bordered table-striped col-md-2' >
				<thead>
				<tr>
				<th colspan='2'><center>GENEL TOPLAM</center></th>
				</tr>
				</thead>
				<tbody>
				<tr>
				<td>Vardiyalar Genel Toplamı</td>
				";
				$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam;
				$message .= "
				<td>$vardiyatoplam ₺</td>
				</tr>
				<tr>
				<td>Depozitolar Genel Toplamı</td>
				";
				$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam;
				$message .= "
				<td>$vardiyadepozitotoplam ₺</td>
				</tr>
				<tr>
				<td>Promosyonlar Genel Toplamı</td>
				";
				$vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon;
				$message .= "
				<td>$vardiyalarpromosyontoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Bakiye İadeleri Genel Toplamı</td>
				";
				$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam;
				$message .= "
				<td>$vardiyabakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Depozito İadeleri Genel Toplamı</td>
				";
				$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam;
				$message .= "
				<td>$vardiyadepozitoiadetoplam ₺</td>
				</tr>
				</tbody>
				</table>
				</body>
				</html>";
			}elseif ($counter == 3) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$raporlar1 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor1 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor11 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor1 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor2 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor22 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor2 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor3 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor33 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor3 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$message="$datee
				<!DOCTYPE html>
				<html>
				<body>
				<style>
				table, th, td {
					border: 1px solid;
				}
				p {
					text-align: center;
				}
				hr {
					border: 7px solid red;
					border-radius: 5px;
				}
				</style>
				<b><label>1. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar1 as $vardiya1) 
				{ 
					$kart_id = $raporlar1->kart_id;
					$personel_id = $raporlar1->personel_id;
					$eklenecek_bakiye = $raporlar1->eklenecek_bakiye;
					$datee_time = $raporlar1->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>1. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor1 as $iaderapor1){
					$kart_id = $iaderapor1->kart_id;
					$personel = $iaderapor1->personel;
					$iade_bakiye = $iaderapor1->iade_bakiye;
					$iade_depozito = $iaderapor1->iade_depozito;
					$datee_time = $iaderapor1->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.= "
				<b><label>2. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar2 as $vardiya2) 
				{ 
					$kart_id = $vardiya2->kart_id;
					$personel_id = $vardiya2->personel_id;
					$eklenecek_bakiye = $vardiya2->eklenecek_bakiye;
					$datee_time = $vardiya2->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>2. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor2 as $iaderapor2){
					$kart_id = $iaderapor2->kart_id;
					$personel = $iaderapor2->personel;
					$iade_bakiye = $iaderapor2->iade_bakiye;
					$iade_depozito = $iaderapor2->iade_depozito;
					$datee_time = $iaderapor2->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>3. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar3 as $vardiya3) 
				{ 
					$kart_id = $vardiya3->kart_id;
					$personel_id = $vardiya3->personel_id;
					$eklenecek_bakiye = $vardiya3->eklenecek_bakiye;
					$datee_time = $vardiya3->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>3. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor3 as $iaderapor3){
					$kart_id = $iaderapor3->kart_id;
					$personel = $iaderapor3->personel;
					$iade_bakiye = $iaderapor3->iade_bakiye;
					$iade_depozito = $iaderapor3->iade_depozito;
					$datee_time = $iaderapor3->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				
				$message.="
				<b><label>VARDİYALAR TOPLAMI</label></b>
				<div class='row'>
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>1.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>1.Vardiya Toplamı</td>
				";
				foreach ($raporlar1bakiye as $raporlar1bakiye) {
					$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya1toplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor1 as $depozitorapor1) {
						$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
					}
				}
				$message .= "
				<td>$vardiya1depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>1. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar1promosyon) {

					foreach ($raporlar1promosyon as $raporlar1promosyon) {
						$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya1promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				<tr>
				<td>1.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor11) {

					foreach ($iaderapor11 as $iaderapor1) {
						$toplanacakbakiye = $iaderapor1->iade_bakiye;
						$toplanacakdepozito = $iaderapor1->iade_depozito;
						$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya1bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya1depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";

				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>2.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>2.Vardiya Toplamı</td>
				";
				foreach ($raporlar2bakiye as $raporlar2bakiye) {
					$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya2toplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor2 as $depozitorapor2) {
						$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
					}
				}
				$message .= "
				<td>$vardiya2depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>2. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar2promosyon) {

					foreach ($raporlar2promosyon as $raporlar2promosyon) {
						$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya2promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>2.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor22) {

					foreach ($iaderapor22 as $iaderapor2) {
						$toplanacakbakiye = $iaderapor2->iade_bakiye;
						$toplanacakdepozito = $iaderapor2->iade_depozito;
						$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya2bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya2depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>3.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>3.Vardiya Toplamı</td>
				";
				foreach ($raporlar3bakiye as $raporlar3bakiye) {
					$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya3toplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor3) {
					foreach ($depozitorapor3 as $depozitorapor3) {
						$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
					}
				}
				$message .= "
				<td>$vardiya3depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>3. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar3promosyon) {

					foreach ($raporlar3promosyon as $raporlar3promosyon) {
						$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya3promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>3.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor33) {

					foreach ($iaderapor33 as $iaderapor3) {
						$toplanacakbakiye = $iaderapor3->iade_bakiye;
						$toplanacakdepozito = $iaderapor3->iade_depozito;
						$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya3bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya3depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";
				$message .= "
				<table  class='table table-bordered table-striped col-md-2' >
				<thead>
				<tr>
				<th colspan='2'><center>GENEL TOPLAM</center></th>
				</tr>
				</thead>
				<tbody>
				<tr>
				<td>Vardiyalar Genel Toplamı</td>
				";
				$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam;
				$message .= "
				<td>$vardiyatoplam ₺</td>
				</tr>
				<tr>
				<td>Depozitolar Genel Toplamı</td>
				";
				$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam;
				$message .= "
				<td>$vardiyadepozitotoplam ₺</td>
				</tr>
				<tr>
				<td>Promosyonlar Genel Toplamı</td>
				";
				$vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon;
				$message .= "
				<td>$vardiyalarpromosyontoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Bakiye İadeleri Genel Toplamı</td>
				";
				$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam;
				$message .= "
				<td>$vardiyabakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Depozito İadeleri Genel Toplamı</td>
				";
				$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam;
				$message .= "
				<td>$vardiyadepozitoiadetoplam ₺</td>
				</tr>
				</tbody>
				</table>
				";
			}elseif ($counter == 4) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$gorevalmiskisi4 = $gorevalmiskisiler[3]->personel_id;
				$raporlar1 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor1 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor11 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor1 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor2 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor22 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor2 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor3 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor33 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor3 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor4 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor44 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor4 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$message="$datee
				<!DOCTYPE html>
				<html>
				<body>
				<style>
				table, th, td {
					border: 1px solid;
				}
				p {
					text-align: center;
				}
				hr {
					border: 7px solid red;
					border-radius: 5px;
				}
				</style>
				<b><label>1. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar1 as $vardiya1) 
				{ 
					$kart_id = $vardiya1->kart_id;
					$personel_id = $vardiya1->personel_id;
					$eklenecek_bakiye = $vardiya1->eklenecek_bakiye;
					$datee_time = $vardiya1->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>1. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor1 as $iaderapor1){
					$kart_id = $iaderapor1->kart_id;
					$personel = $iaderapor1->personel;
					$iade_bakiye = $iaderapor1->iade_bakiye;
					$iade_depozito = $iaderapor1->iade_depozito;
					$datee_time = $iaderapor1->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.= "
				<b><label>2. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar2 as $vardiya2) 
				{ 
					$kart_id = $vardiya2->kart_id;
					$personel_id = $vardiya2->personel_id;
					$eklenecek_bakiye = $vardiya2->eklenecek_bakiye;
					$datee_time = $vardiya2->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>2. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor2 as $iaderapor2){
					$kart_id = $iaderapor2->kart_id;
					$personel = $iaderapor2->personel;
					$iade_bakiye = $iaderapor2->iade_bakiye;
					$iade_depozito = $iaderapor2->iade_depozito;
					$datee_time = $iaderapor2->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>3. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar3 as $vardiya3) 
				{ 
					$kart_id = $vardiya3->kart_id;
					$personel_id = $vardiya3->personel_id;
					$eklenecek_bakiye = $vardiya3->eklenecek_bakiye;
					$datee_time = $vardiya3->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>3. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor3 as $iaderapor3){
					$kart_id = $iaderapor3->kart_id;
					$personel = $iaderapor3->personel;
					$iade_bakiye = $iaderapor3->iade_bakiye;
					$iade_depozito = $iaderapor3->iade_depozito;
					$datee_time = $iaderapor3->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>4. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar4 as $vardiya4) 
				{ 
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>4. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor4 as $iaderapor4){
					$kart_id = $iaderapor4->kart_id;
					$personel = $iaderapor4->personel;
					$iade_bakiye = $iaderapor4->iade_bakiye;
					$iade_depozito = $iaderapor4->iade_depozito;
					$datee_time = $iaderapor4->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				
				$message.="
				<b><label>VARDİYALAR TOPLAMI</label></b>
				<div class='row'>
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>1.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>1.Vardiya Toplamı</td>
				";
				foreach ($raporlar1bakiye as $raporlar1bakiye) {
					$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya1toplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor1 as $depozitorapor1) {
						$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
					}
				}
				$message .= "
				<td>$vardiya1depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>1. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar1promosyon) {

					foreach ($raporlar1promosyon as $raporlar1promosyon) {
						$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya1promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				<tr>
				<td>1.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor11) {

					foreach ($iaderapor11 as $iaderapor1) {
						$toplanacakbakiye = $iaderapor1->iade_bakiye;
						$toplanacakdepozito = $iaderapor1->iade_depozito;
						$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya1bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya1depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";

				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>2.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>2.Vardiya Toplamı</td>
				";
				foreach ($raporlar2bakiye as $raporlar2bakiye) {
					$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya2toplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor2 as $depozitorapor2) {
						$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
					}
				}
				$message .= "
				<td>$vardiya2depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>2. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar2promosyon) {

					foreach ($raporlar2promosyon as $raporlar2promosyon) {
						$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya2promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>2.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor22) {

					foreach ($iaderapor22 as $iaderapor2) {
						$toplanacakbakiye = $iaderapor2->iade_bakiye;
						$toplanacakdepozito = $iaderapor2->iade_depozito;
						$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya2bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya2depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>3.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>3.Vardiya Toplamı</td>
				";
				foreach ($raporlar3bakiye as $raporlar3bakiye) {
					$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya3toplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor3) {
					foreach ($depozitorapor3 as $depozitorapor3) {
						$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
					}
				}
				$message .= "
				<td>$vardiya3depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>3. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar3promosyon) {

					foreach ($raporlar3promosyon as $raporlar3promosyon) {
						$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya3promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>3.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor33) {

					foreach ($iaderapor33 as $iaderapor3) {
						$toplanacakbakiye = $iaderapor3->iade_bakiye;
						$toplanacakdepozito = $iaderapor3->iade_depozito;
						$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya3bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya3depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>4.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>4.Vardiya Toplamı</td>
				";
				foreach ($raporlar4bakiye as $raporlar4bakiye) {
					$vardiya4toplam = $vardiya4toplam + $raporlar4bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya4toplam ₺</td>
				</tr>
				<tr>
				<td>4.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor4) {
					foreach ($depozitorapor4 as $depozitorapor4) {
						$vardiya4depozitotoplam = $vardiya4depozitotoplam + $depozitorapor4->depozito;
					}
				}
				$message .= "
				<td>$vardiya4depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>4. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar4promosyon) {

					foreach ($raporlar4promosyon as $raporlar4promosyon) {
						$vardiya4promosyon = $vardiya4promosyon + $raporlar4promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya4promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>4.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor44) {

					foreach ($iaderapor44 as $iaderapor4) {
						$toplanacakbakiye = $iaderapor4->iade_bakiye;
						$toplanacakdepozito = $iaderapor4->iade_depozito;
						$vardiya4bakiyeiadetoplam = $vardiya4bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya4depozitoiadetoplam = $vardiya4depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya4bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>4.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya4depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";
				$message .= "
				<table  class='table table-bordered table-striped col-md-2' >
				<thead>
				<tr>
				<th colspan='2'><center>GENEL TOPLAM</center></th>
				</tr>
				</thead>
				<tbody>
				<tr>
				<td>Vardiyalar Genel Toplamı</td>
				";
				$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam + $vardiya4toplam;
				$message .= "
				<td>$vardiyatoplam ₺</td>
				</tr>
				<tr>
				<td>Depozitolar Genel Toplamı</td>
				";
				$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam + $vardiya4depozitotoplam;
				$message .= "
				<td>$vardiyadepozitotoplam ₺</td>
				</tr>
				<tr>
				<td>Promosyonlar Genel Toplamı</td>
				";
				$vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon + $vardiya4promosyon;
				$message .= "
				<td>$vardiyalarpromosyontoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Bakiye İadeleri Genel Toplamı</td>
				";
				$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam + $vardiya4bakiyeiadetoplam;
				$message .= "
				<td>$vardiyabakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Depozito İadeleri Genel Toplamı</td>
				";
				$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam + $vardiya4depozitoiadetoplam;
				$message .= "
				<td>$vardiyadepozitoiadetoplam ₺</td>
				</tr>
				</tbody>
				</table>
				";
			}elseif ($counter == 5) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$gorevalmiskisi4 = $gorevalmiskisiler[3]->personel_id;
				$gorevalmiskisi5 = $gorevalmiskisiler[4]->personel_id;
				$raporlar1 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor1 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor11 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor1 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor2 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor22 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor2 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor3 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor33 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor3 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor4 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor44 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor4 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar5 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor5 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor55 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor5 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar5bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar5promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$message="$datee
				<!DOCTYPE html>
				<html>
				<body>
				<style>
				table, th, td {
					border: 1px solid;
				}
				p {
					text-align: center;
				}
				hr {
					border: 7px solid red;
					border-radius: 5px;
				}
				</style>
				<b><label>1. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar1 as $vardiya1) 
				{ 
					$kart_id = $vardiya1->kart_id;
					$personel_id = $vardiya1->personel_id;
					$eklenecek_bakiye = $vardiya1->eklenecek_bakiye;
					$datee_time = $vardiya1->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>1. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor1 as $iaderapor1){
					$kart_id = $iaderapor1->kart_id;
					$personel = $iaderapor1->personel;
					$iade_bakiye = $iaderapor1->iade_bakiye;
					$iade_depozito = $iaderapor1->iade_depozito;
					$datee_time = $iaderapor1->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.= "
				<b><label>2. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar2 as $vardiya2) 
				{ 
					$kart_id = $vardiya2->kart_id;
					$personel_id = $vardiya2->personel_id;
					$eklenecek_bakiye = $vardiya2->eklenecek_bakiye;
					$datee_time = $vardiya2->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>2. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor2 as $iaderapor2){
					$kart_id = $iaderapor2->kart_id;
					$personel = $iaderapor2->personel;
					$iade_bakiye = $iaderapor2->iade_bakiye;
					$iade_depozito = $iaderapor2->iade_depozito;
					$datee_time = $iaderapor2->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>3. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar3 as $vardiya3) 
				{ 
					$kart_id = $vardiya3->kart_id;
					$personel_id = $vardiya3->personel_id;
					$eklenecek_bakiye = $vardiya3->eklenecek_bakiye;
					$datee_time = $vardiya3->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>3. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor3 as $iaderapor3){
					$kart_id = $iaderapor3->kart_id;
					$personel = $iaderapor3->personel;
					$iade_bakiye = $iaderapor3->iade_bakiye;
					$iade_depozito = $iaderapor3->iade_depozito;
					$datee_time = $iaderapor3->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>4. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar4 as $vardiya4) 
				{ 
					$kart_id = $vardiya4->kart_id;
					$personel_id = $vardiya4->personel_id;
					$eklenecek_bakiye = $vardiya4->eklenecek_bakiye;
					$datee_time = $vardiya4->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>4. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor4 as $iaderapor4){
					$kart_id = $iaderapor4->kart_id;
					$personel = $iaderapor4->personel;
					$iade_bakiye = $iaderapor4->iade_bakiye;
					$iade_depozito = $iaderapor4->iade_depozito;
					$datee_time = $iaderapor4->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>5. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar5 as $vardiya5) 
				{ 
					$kart_id = $vardiya5->kart_id;
					$personel_id = $vardiya5->personel_id;
					$eklenecek_bakiye = $vardiya5->eklenecek_bakiye;
					$datee_time = $vardiya5->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>5. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor5 as $iaderapor5){
					$kart_id = $iaderapor5->kart_id;
					$personel = $iaderapor5->personel;
					$iade_bakiye = $iaderapor5->iade_bakiye;
					$iade_depozito = $iaderapor5->iade_depozito;
					$datee_time = $iaderapor5->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				
				$message.="
				<b><label>VARDİYALAR TOPLAMI</label></b>
				<div class='row'>
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>1.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>1.Vardiya Toplamı</td>
				";
				foreach ($raporlar1bakiye as $raporlar1bakiye) {
					$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya1toplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor1 as $depozitorapor1) {
						$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
					}
				}
				$message .= "
				<td>$vardiya1depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>1. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar1promosyon) {

					foreach ($raporlar1promosyon as $raporlar1promosyon) {
						$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya1promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				<tr>
				<td>1.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor11) {

					foreach ($iaderapor11 as $iaderapor1) {
						$toplanacakbakiye = $iaderapor1->iade_bakiye;
						$toplanacakdepozito = $iaderapor1->iade_depozito;
						$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya1bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya1depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";

				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>2.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>2.Vardiya Toplamı</td>
				";
				foreach ($raporlar2bakiye as $raporlar2bakiye) {
					$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya2toplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor2 as $depozitorapor2) {
						$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
					}
				}
				$message .= "
				<td>$vardiya2depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>2. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar2promosyon) {

					foreach ($raporlar2promosyon as $raporlar2promosyon) {
						$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya2promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>2.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor22) {

					foreach ($iaderapor22 as $iaderapor2) {
						$toplanacakbakiye = $iaderapor2->iade_bakiye;
						$toplanacakdepozito = $iaderapor2->iade_depozito;
						$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya2bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya2depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>3.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>3.Vardiya Toplamı</td>
				";
				foreach ($raporlar3bakiye as $raporlar3bakiye) {
					$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya3toplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor3) {
					foreach ($depozitorapor3 as $depozitorapor3) {
						$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
					}
				}
				$message .= "
				<td>$vardiya3depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>3. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar3promosyon) {

					foreach ($raporlar3promosyon as $raporlar3promosyon) {
						$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya3promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>3.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor33) {

					foreach ($iaderapor33 as $iaderapor3) {
						$toplanacakbakiye = $iaderapor3->iade_bakiye;
						$toplanacakdepozito = $iaderapor3->iade_depozito;
						$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya3bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya3depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>4.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>4.Vardiya Toplamı</td>
				";
				foreach ($raporlar4bakiye as $raporlar4bakiye) {
					$vardiya4toplam = $vardiya4toplam + $raporlar4bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya4toplam ₺</td>
				</tr>
				<tr>
				<td>4.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor4) {
					foreach ($depozitorapor4 as $depozitorapor4) {
						$vardiya4depozitotoplam = $vardiya4depozitotoplam + $depozitorapor4->depozito;
					}
				}
				$message .= "
				<td>$vardiya4depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>4. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar4promosyon) {

					foreach ($raporlar4promosyon as $raporlar4promosyon) {
						$vardiya4promosyon = $vardiya4promosyon + $raporlar4promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya4promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>4.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor44) {

					foreach ($iaderapor44 as $iaderapor4) {
						$toplanacakbakiye = $iaderapor4->iade_bakiye;
						$toplanacakdepozito = $iaderapor4->iade_depozito;
						$vardiya4bakiyeiadetoplam = $vardiya4bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya4depozitoiadetoplam = $vardiya4depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya4bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>4.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya4depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";



				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>5.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>5.Vardiya Toplamı</td>
				";
				foreach ($raporlar5bakiye as $raporlar5bakiye) {
					$vardiya5toplam = $vardiya5toplam + $raporlar5bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya5toplam ₺</td>
				</tr>
				<tr>
				<td>5.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor5) {
					foreach ($depozitorapor5 as $depozitorapor5) {
						$vardiya5depozitotoplam = $vardiya5depozitotoplam + $depozitorapor5->depozito;
					}
				}
				$message .= "
				<td>$vardiya5depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>5. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar5promosyon) {

					foreach ($raporlar5promosyon as $raporlar5promosyon) {
						$vardiya5promosyon = $vardiya5promosyon + $raporlar5promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya5promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>5.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor55) {

					foreach ($iaderapor55 as $iaderapor5) {
						$toplanacakbakiye = $iaderapor5->iade_bakiye;
						$toplanacakdepozito = $iaderapor5->iade_depozito;
						$vardiya5bakiyeiadetoplam = $vardiya5bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya5depozitoiadetoplam = $vardiya5depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya5bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>5.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya5depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";
				$message .= "
				<table  class='table table-bordered table-striped col-md-2' >
				<thead>
				<tr>
				<th colspan='2'><center>GENEL TOPLAM</center></th>
				</tr>
				</thead>
				<tbody>
				<tr>
				<td>Vardiyalar Genel Toplamı</td>
				";
				$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam + $vardiya4toplam + $vardiya5toplam;
				$message .= "
				<td>$vardiyatoplam ₺</td>
				</tr>
				<tr>
				<td>Depozitolar Genel Toplamı</td>
				";
				$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam + $vardiya4depozitotoplam + $vardiya5depozitotoplam;
				$message .= "
				<td>$vardiyadepozitotoplam ₺</td>
				</tr>
				<tr>
				<td>Promosyonlar Genel Toplamı</td>
				";
				$vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon + $vardiya4promosyon + $vardiya5promosyon;
				$message .= "
				<td>$vardiyalarpromosyontoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Bakiye İadeleri Genel Toplamı</td>
				";
				$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam + $vardiya4bakiyeiadetoplam + $vardiya5bakiyeiadetoplam;
				$message .= "
				<td>$vardiyabakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Depozito İadeleri Genel Toplamı</td>
				";
				$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam + $vardiya4depozitoiadetoplam + $vardiya5depozitoiadetoplam;
				$message .= "
				<td>$vardiyadepozitoiadetoplam ₺</td>
				</tr>
				</tbody>
				</table>
				";
			}elseif ($counter == 6) {
				$gorevalmiskisi1 = $gorevalmiskisiler[0]->personel_id;
				$gorevalmiskisi2 = $gorevalmiskisiler[1]->personel_id;
				$gorevalmiskisi3 = $gorevalmiskisiler[2]->personel_id;
				$gorevalmiskisi4 = $gorevalmiskisiler[3]->personel_id;
				$gorevalmiskisi5 = $gorevalmiskisiler[4]->personel_id;
				$gorevalmiskisi6 = $gorevalmiskisiler[5]->personel_id;
				$raporlar1 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor1 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor11 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor1 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi1' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar1promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi1' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor2 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor22 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor2 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi2' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar2promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi2' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor3 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor33 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor3 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi3' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar3promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi3' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor4 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor44 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor4 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi4' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar4promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi4' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar5 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor5 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor55 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor5 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi5' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar5bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar5promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi5' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar6 = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor6 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$iaderapor66 = kiade::query("SELECT * FROM iade WHERE personel = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$depozitorapor6 = vardiyadepozito::query("SELECT * FROM vardiya_depozito WHERE personel_id = '$gorevalmiskisi6' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar6bakiye = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi6' AND kart_id NOT IN ($promosyonkartlari) AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$raporlar6promosyon = bakiye::query("SELECT * FROM bakiye_yukle WHERE personel_id = '$gorevalmiskisi6' AND kart_id = '$promosyonkartlari' AND date_time BETWEEN '$combinedDT' AND '$date_time' GROUP BY date_time");
				$message="$datee
				<!DOCTYPE html>
				<html>
				<body>
				<style>
				table, th, td {
					border: 1px solid;
				}
				p {
					text-align: center;
				}
				hr {
					border: 7px solid red;
					border-radius: 5px;
				}
				</style>
				<b><label>1. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar1 as $vardiya1) 
				{ 
					$kart_id = $vardiya1->kart_id;
					$personel_id = $vardiya1->personel_id;
					$eklenecek_bakiye = $vardiya1->eklenecek_bakiye;
					$datee_time = $vardiya1->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>1. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor1 as $iaderapor1){
					$kart_id = $iaderapor1->kart_id;
					$personel = $iaderapor1->personel;
					$iade_bakiye = $iaderapor1->iade_bakiye;
					$iade_depozito = $iaderapor1->iade_depozito;
					$datee_time = $iaderapor1->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.= "
				<b><label>2. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar2 as $vardiya2) 
				{ 
					$kart_id = $vardiya2->kart_id;
					$personel_id = $vardiya2->personel_id;
					$eklenecek_bakiye = $vardiya2->eklenecek_bakiye;
					$datee_time = $vardiya2->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>2. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor2 as $iaderapor2){
					$kart_id = $iaderapor2->kart_id;
					$personel = $iaderapor2->personel;
					$iade_bakiye = $iaderapor2->iade_bakiye;
					$iade_depozito = $iaderapor2->iade_depozito;
					$datee_time = $iaderapor2->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>3. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar3 as $vardiya3) 
				{ 
					$kart_id = $vardiya3->kart_id;
					$personel_id = $vardiya3->personel_id;
					$eklenecek_bakiye = $vardiya3->eklenecek_bakiye;
					$datee_time = $vardiya3->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>3. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor3 as $iaderapor3){
					$kart_id = $iaderapor3->kart_id;
					$personel = $iaderapor3->personel;
					$iade_bakiye = $iaderapor3->iade_bakiye;
					$iade_depozito = $iaderapor3->iade_depozito;
					$datee_time = $iaderapor3->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>4. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar4 as $vardiya4) 
				{ 
					$kart_id = $vardiya4->kart_id;
					$personel_id = $vardiya4->personel_id;
					$eklenecek_bakiye = $vardiya4->eklenecek_bakiye;
					$datee_time = $vardiya4->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>4. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor4 as $iaderapor4){
					$kart_id = $iaderapor4->kart_id;
					$personel = $iaderapor4->personel;
					$iade_bakiye = $iaderapor4->iade_bakiye;
					$iade_depozito = $iaderapor4->iade_depozito;
					$datee_time = $iaderapor4->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>5. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar5 as $vardiya5) 
				{ 
					$kart_id = $vardiya5->kart_id;
					$personel_id = $vardiya5->personel_id;
					$eklenecek_bakiye = $vardiya5->eklenecek_bakiye;
					$datee_time = $vardiya5->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>5. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor5 as $iaderapor5){
					$kart_id = $iaderapor5->kart_id;
					$personel = $iaderapor5->personel;
					$iade_bakiye = $iaderapor5->iade_bakiye;
					$iade_depozito = $iaderapor5->iade_depozito;
					$datee_time = $iaderapor5->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>6. VARDİYA</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>Yükleyen Personel</th>
				<th>Eklenen Bakiye</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($raporlar6 as $vardiya6) 
				{ 
					$kart_id = $vardiya6->kart_id;
					$personel_id = $vardiya6->personel_id;
					$eklenecek_bakiye = $vardiya6->eklenecek_bakiye;
					$datee_time = $vardiya6->date_time;
					$message .= "
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel_id</p>
					</td>
					<td>
					<p>$eklenecek_bakiye ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>
					";
				} 
				$message .= "
				<b><label>6. VARDİYA İADELER TABLOSU</label></b>
				<table>
				<thead>
				<tr>
				<th>Kart ID</th>
				<th>İade Eden Personel</th>
				<th>İade Edilen Bakiye</th>
				<th>İade Edilen depozito</th>
				<th>Tarih</th>
				</tr>
				</thead>
				<tbody>";
				foreach ($iaderapor6 as $iaderapor6){
					$kart_id = $iaderapor6->kart_id;
					$personel = $iaderapor6->personel;
					$iade_bakiye = $iaderapor6->iade_bakiye;
					$iade_depozito = $iaderapor6->iade_depozito;
					$datee_time = $iaderapor6->date_time;
					$message .= "					
					<tr>
					<td>
					<p>$kart_id</p>
					</td>
					<td>
					<p>$personel</p>
					</td>
					<td>
					<p>$iade_bakiye ₺</p>
					</td>
					<td>
					<p>$iade_depozito ₺</p>
					</td>
					<td>
					<p>$datee_time</p>
					</td>
					</tr>";
				}
				$message .= "
				</tbody>
				</table>
				<br><hr>";
				$message.="
				<b><label>VARDİYALAR TOPLAMI</label></b>
				<div class='row'>
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>1.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>1.Vardiya Toplamı</td>
				";
				foreach ($raporlar1bakiye as $raporlar1bakiye) {
					$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya1toplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor1 as $depozitorapor1) {
						$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
					}
				}
				$message .= "
				<td>$vardiya1depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>1. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar1promosyon) {

					foreach ($raporlar1promosyon as $raporlar1promosyon) {
						$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya1promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				<tr>
				<td>1.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor11) {

					foreach ($iaderapor11 as $iaderapor1) {
						$toplanacakbakiye = $iaderapor1->iade_bakiye;
						$toplanacakdepozito = $iaderapor1->iade_depozito;
						$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya1bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>1.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya1depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";

				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>2.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>2.Vardiya Toplamı</td>
				";
				foreach ($raporlar2bakiye as $raporlar2bakiye) {
					$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya2toplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor1) {
					foreach ($depozitorapor2 as $depozitorapor2) {
						$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
					}
				}
				$message .= "
				<td>$vardiya2depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>2. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar2promosyon) {

					foreach ($raporlar2promosyon as $raporlar2promosyon) {
						$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya2promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>2.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor22) {

					foreach ($iaderapor22 as $iaderapor2) {
						$toplanacakbakiye = $iaderapor2->iade_bakiye;
						$toplanacakdepozito = $iaderapor2->iade_depozito;
						$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya2bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>2.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya2depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>3.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>3.Vardiya Toplamı</td>
				";
				foreach ($raporlar3bakiye as $raporlar3bakiye) {
					$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya3toplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor3) {
					foreach ($depozitorapor3 as $depozitorapor3) {
						$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
					}
				}
				$message .= "
				<td>$vardiya3depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>3. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar3promosyon) {

					foreach ($raporlar3promosyon as $raporlar3promosyon) {
						$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya3promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>3.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor33) {

					foreach ($iaderapor33 as $iaderapor3) {
						$toplanacakbakiye = $iaderapor3->iade_bakiye;
						$toplanacakdepozito = $iaderapor3->iade_depozito;
						$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya3bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>3.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya3depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";


				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>4.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>4.Vardiya Toplamı</td>
				";
				foreach ($raporlar4bakiye as $raporlar4bakiye) {
					$vardiya4toplam = $vardiya4toplam + $raporlar4bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya4toplam ₺</td>
				</tr>
				<tr>
				<td>4.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor4) {
					foreach ($depozitorapor4 as $depozitorapor4) {
						$vardiya4depozitotoplam = $vardiya4depozitotoplam + $depozitorapor4->depozito;
					}
				}
				$message .= "
				<td>$vardiya4depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>4. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar4promosyon) {

					foreach ($raporlar4promosyon as $raporlar4promosyon) {
						$vardiya4promosyon = $vardiya4promosyon + $raporlar4promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya4promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>4.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor44) {

					foreach ($iaderapor44 as $iaderapor4) {
						$toplanacakbakiye = $iaderapor4->iade_bakiye;
						$toplanacakdepozito = $iaderapor4->iade_depozito;
						$vardiya4bakiyeiadetoplam = $vardiya4bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya4depozitoiadetoplam = $vardiya4depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya4bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>4.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya4depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";



				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>5.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>5.Vardiya Toplamı</td>
				";
				foreach ($raporlar5bakiye as $raporlar5bakiye) {
					$vardiya5toplam = $vardiya5toplam + $raporlar5bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya5toplam ₺</td>
				</tr>
				<tr>
				<td>5.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor5) {
					foreach ($depozitorapor5 as $depozitorapor5) {
						$vardiya5depozitotoplam = $vardiya5depozitotoplam + $depozitorapor5->depozito;
					}
				}
				$message .= "
				<td>$vardiya5depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>5. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar5promosyon) {

					foreach ($raporlar5promosyon as $raporlar5promosyon) {
						$vardiya5promosyon = $vardiya5promosyon + $raporlar5promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya5promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>5.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor55) {

					foreach ($iaderapor55 as $iaderapor5) {
						$toplanacakbakiye = $iaderapor5->iade_bakiye;
						$toplanacakdepozito = $iaderapor5->iade_depozito;
						$vardiya5bakiyeiadetoplam = $vardiya5bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya5depozitoiadetoplam = $vardiya5depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya5bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>5.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya5depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";



				$message .= "
				<table>
				<thead>
				<tr>
				<th colspan='2'><center>6.VARDİYA</center></th>
				</tr>
				</thead>
				<tbody>";
				$message .= "
				<tr>
				<td>6.Vardiya Toplamı</td>
				";
				foreach ($raporlar6bakiye as $raporlar6bakiye) {
					$vardiya6toplam = $vardiya6toplam + $raporlar6bakiye->eklenecek_bakiye;
				}
				$message .= "
				<td>$vardiya6toplam ₺</td>
				</tr>
				<tr>
				<td>6.Vardiya Depozito Toplamı</td>
				";
				if ($depozitorapor6) {
					foreach ($depozitorapor6 as $depozitorapor6) {
						$vardiya6depozitotoplam = $vardiya6depozitotoplam + $depozitorapor6->depozito;
					}
				}
				$message .= "
				<td>$vardiya6depozitotoplam ₺</td>
				</tr>
				<tr>
				<td>6. Vardiya Promosyon Toplamı</td>
				<td>
				";
				if ($raporlar6promosyon) {

					foreach ($raporlar6promosyon as $raporlar6promosyon) {
						$vardiya6promosyon = $vardiya6promosyon + $raporlar6promosyon->eklenecek_bakiye;
					}
					$message .= "$vardiya6promosyon ₺</td>";

				}else{
					$message .= "0 ₺</td>";
				}
				$message .= "
				
				</tr>
				</tr>
				<tr>
				<td>6.Vardiya Kart Bakiye İadesi</td>
				";
				if ($iaderapor66) {

					foreach ($iaderapor66 as $iaderapor6) {
						$toplanacakbakiye = $iaderapor6->iade_bakiye;
						$toplanacakdepozito = $iaderapor6->iade_depozito;
						$vardiya6bakiyeiadetoplam = $vardiya6bakiyeiadetoplam + $toplanacakbakiye;
						$vardiya6depozitoiadetoplam = $vardiya6depozitoiadetoplam + $toplanacakdepozito;
					}
				}
				$message .= "
				<td>$vardiya6bakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>6.Vardiya Kart Depozito İadesi</td>
				<td>$vardiya6depozitoiadetoplam ₺</td>
				</tr>

				</tbody>
				</table>
				&nbsp;&nbsp;&nbsp;
				";
				$message .= "
				<table  class='table table-bordered table-striped col-md-2' >
				<thead>
				<tr>
				<th colspan='2'><center>GENEL TOPLAM</center></th>
				</tr>
				</thead>
				<tbody>
				<tr>
				<td>Vardiyalar Genel Toplamı</td>
				";
				$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam + $vardiya4toplam + $vardiya5toplam + $vardiya6toplam;
				$message .= "
				<td>$vardiyatoplam ₺</td>
				</tr>
				<tr>
				<td>Depozitolar Genel Toplamı</td>
				";
				$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam + $vardiya4depozitotoplam + $vardiya5depozitotoplam + $vardiya6depozitotoplam;
				$message .= "
				<td>$vardiyadepozitotoplam ₺</td>
				</tr>
				<tr>
				<td>Promosyonlar Genel Toplamı</td>
				";
				$vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon + $vardiya4promosyon + $vardiya5promosyon + $vardiya6promosyon;
				$message .= "
				<td>$vardiyalarpromosyontoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Bakiye İadeleri Genel Toplamı</td>
				";
				$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam + $vardiya4bakiyeiadetoplam + $vardiya5bakiyeiadetoplam + $vardiya6bakiyeiadetoplam;
				$message .= "
				<td>$vardiyabakiyeiadetoplam ₺</td>
				</tr>
				<tr>
				<td>Kart Depozito İadeleri Genel Toplamı</td>
				";
				$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam + $vardiya4depozitoiadetoplam + $vardiya5depozitoiadetoplam + $vardiya6depozitoiadetoplam;
				$message .= "
				<td>$vardiyadepozitoiadetoplam ₺</td>
				</tr>
				</tbody>
				</table>
				";


			}
		}
		$from_name = "TECHNOLOBAL BİLİŞİM BİLGİLENDİRME SİSTEMİ";
		$from_address = "<EMAIL>";
		$to_name = "Receiver Name";
		$to_address = "<EMAIL>";
		$subject = "Petrol Otomasyonu Günlük Özet";
		$description = "Petrol Otomasyonu Günlük Özet"; 
		$datee = date("d.m.y H:i:s");
		$this->email->initialize($config); 
		$this->email->from($from_address,$from_name);
		$this->email->to($to_address);
		$this->email->subject($subject);
		$this->email->message($message);
		$sendd=$this->email->send();
		if($sendd)
		{
			flash('success','check','BAŞARILI!!!','E-Posta Gönderme İşlemi Başarıyla Gerçekleşmiştir...');
			back();
		}else
		{
			flash('danger','window-close','BAŞARISIZ!!!','E-Mail Gönderilemedi ! Problemin Devam Etmesi Durumunda Technolobal Bilişime Başvurunuz');
			back();
			//echo "işlem başarısız";
			//echo $this->email->print_debugger();
		}
//return mail($to_address, $subject, $message, $headers);



		?>
