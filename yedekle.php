<?php 
//
//
//
//
//
//
//
//
//
//
//
//--------- B<PERSON> KODLAR LOCAL DATABASEDEKI BELIRLI TABLOLARI PETROLUN CAMLI (ARA) SERVERLERINE POST EDEREK ONLARIN DATABASELERINE KAYIT EDER
//
//
//
//
//
//
//
//
//
//
//
//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BASLANGIC
$logbackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\loglar_backup.sql';

if (file_exists($logbackupyol)) {
	if (unlink($logbackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}

$bakiyeyuklebackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_backup.sql';

if (file_exists($bakiyeyuklebackupyol)) {
	if (unlink($bakiyeyuklebackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$musteribackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

if (file_exists($musteribackupyol)) {
	if (unlink($musteribackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$iadebackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\iade_backup.sql';

if (file_exists($iadebackupyol)) {
	if (unlink($iadebackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$depozitobackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\depozito_backup.sql';

if (file_exists($depozitobackupyol)) {
	if (unlink($depozitobackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$promosyonlarbackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\promosyonlar_backup.sql';

if (file_exists($promosyonlarbackupyol)) {
	if (unlink($promosyonlarbackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BITIS

//*****************************************************************************************************

//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BASLANGIC
$host = "localhost";
$db_user = "root";
$db_pass = "";
$backup_dir = "C:/xampp/htdocs/petrolsistemi/yedekler/";

$db_names = "petrolsistemi";
$connn = new mysqli($host, $db_user, $db_pass, $db_names);
$table_name_loglar = "loglar";
$backup_file_loglar = $backup_dir . "loglar_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name_loglar} > {$backup_file_loglar}");
$table_name_bakiyeyukle = "bakiye_yukle";
$backup_file_bakiye = $backup_dir . "bakiyeyukle_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name_bakiyeyukle} > {$backup_file_bakiye}");
$table_name_iade = "iade";
$backup_file_iade = $backup_dir . "iade_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name_iade} > {$backup_file_iade}");
$table_name_depozito = "vardiya_depozito";
$backup_file_depozito = $backup_dir . "depozito_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name_depozito} > {$backup_file_depozito}");
$table_name_promosyonlar = "promosyonlar";
$backup_file_promosyonlar = $backup_dir . "promosyonlar_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name_promosyonlar} > {$backup_file_promosyonlar}");
$table_name_musteri = "musteri";
$backup_file_musteri = $backup_dir . "musteri_backup.sql";
exec("C:/xampp/mysql/bin/mysqldump.exe --user={$db_user} --password={$db_pass} --host={$host} {$db_names} {$table_name_musteri} > {$backup_file_musteri}");
//ONLINE DAKI MUSTERI TABLOSUNU CEK VE ISTENILEN DOSYAYA KAYDET -- BITIS

//*****************************************************************************************************

//ONLINE DAN CEKILEN loglar SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BASLANGIC
$servername = "***********";
$username = "u411458722_ulaopet";
$password = "I@HjABh5u#";
$dbname = "u411458722_ulaopet";

$conn = new mysqli($servername, $username, $password, $dbname);

if ($conn->connect_error) {
	die("Bağlantı hatası: " . $conn->connect_error);
}

$sqlFile_loglar = 'C:\xampp\htdocs\petrolsistemi\yedekler\loglar_backup.sql';

$sql_loglar = file_get_contents($sqlFile_loglar);

$queries_loglar = explode(';', $sql_loglar);

foreach ($queries_loglar as $query_loglar) {
	$query_loglar = trim($query_loglar);
	if (!empty($query_loglar)) {
		$result = $conn->query($query_loglar);
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}
$sqlFile_bakiye = 'C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_backup.sql';

$sql_bakiye = file_get_contents($sqlFile_bakiye);

$queries_bakiye = explode(';', $sql_bakiye);

foreach ($queries_bakiye as $query_bakiye) {
	$query_bakiye = trim($query_bakiye);
	if (!empty($query_bakiye)) {
		$result = $conn->query($query_bakiye); 
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}
$sqlFile_musteri = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

$sql_musteri = file_get_contents($sqlFile_musteri);

$queries_musteri = explode(';', $sql_musteri);

foreach ($queries_musteri as $query_musteri) {
	$query_musteri = trim($query_musteri);
	if (!empty($query_musteri)) {
		$result = $conn->query($query_musteri);
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}
$sqlFile_iade = 'C:\xampp\htdocs\petrolsistemi\yedekler\iade_backup.sql';

$sql_iade = file_get_contents($sqlFile_iade);

$queries_iade = explode(';', $sql_iade);

foreach ($queries_iade as $query_iade) {
	$query_iade = trim($query_iade);
	if (!empty($query_iade)) {
		$result = $conn->query($query_iade);
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}
$sqlFile_depozito = 'C:\xampp\htdocs\petrolsistemi\yedekler\depozito_backup.sql';

$sql_depozito = file_get_contents($sqlFile_depozito);

$queries_depozito = explode(';', $sql_depozito);

foreach ($queries_depozito as $query_depozito) {
	$query_depozito = trim($query_depozito);
	if (!empty($query_depozito)) {
		$result = $conn->query($query_depozito);
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}
$sqlFile_promosyonlar = 'C:\xampp\htdocs\petrolsistemi\yedekler\promosyonlar_backup.sql';

$sql_promosyonlar = file_get_contents($sqlFile_promosyonlar);

$queries_promosyonlar = explode(';', $sql_promosyonlar);

foreach ($queries_promosyonlar as $query_promosyonlar) {
	$query_promosyonlar = trim($query_promosyonlar);
	if (!empty($query_promosyonlar)) {
		$result = $conn->query($query_promosyonlar);
		if (!$result) {
			echo "Hata: " . $conn->error;
		}
	}
}
$conn->close();
//ONLINE DAN CEKILEN SQL DOSYASININ MEVCUT TABLOYA AKTARILMASI -- BITIS

//*****************************************************************************************************

//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BASLANGIC
$logbackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\loglar_backup.sql';

if (file_exists($logbackupyol)) {
	if (unlink($logbackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$bakiyeyuklebackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\bakiyeyukle_backup.sql';

if (file_exists($bakiyeyuklebackupyol)) {
	if (unlink($bakiyeyuklebackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$iadebackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\iade_backup.sql';

if (file_exists($iadebackupyol)) {
	if (unlink($iadebackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$depozitobackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\depozito_backup.sql';

if (file_exists($depozitobackupyol)) {
	if (unlink($depozitobackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$promosyonlarbackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\promosyonlar_backup.sql';

if (file_exists($promosyonlarbackupyol)) {
	if (unlink($promosyonlarbackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
$musteribackupyol = 'C:\xampp\htdocs\petrolsistemi\yedekler\musteri_backup.sql';

if (file_exists($musteribackupyol)) {
	if (unlink($musteribackupyol)) {
		echo "Dosya başarıyla silindi.";
	} else {
		echo "Dosya silinemedi.";
	}
} else {
	echo "Dosya mevcut değil.";
}
//NE OLUR NE OLMAZ EGER HALA SILINMEMIS BIR GECMIS SQL YEDEGI VARSA SILEN FOKSIYON -- BITIS










?>