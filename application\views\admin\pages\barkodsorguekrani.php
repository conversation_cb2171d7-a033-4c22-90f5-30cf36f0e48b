<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<section class="content">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xl-12">
				<div class="box box-solid">
					<div class="box-body">
						<table id="havaleler" class="table table-bordered table-striped" style="width:100%">
							<thead>
								<tr>
									<th>İşlem Numarası</th>
									<th>Barkod Numarası</th>
									<th>Mevcut Bakiye</th>
									<th>Oluşturan Personel</th>
									<th>Tarih\Saat</th>
									<th>İşlemler</th>
								</tr>
							</thead>
							<?php foreach ($musteriler as $musteri) 
								{ $sorgu=bakiye::select(['kart_id'=>$musteri->kart_id]); ?>
							<tbody>
									<tr>
										<td>
											<p><?php echo $musteriler[0]->id; ?></p>
										</td>
										<td>
											<p><?php echo $musteriler[0]->kart_id; ?></p>
										</td>
										<td>
											<p><?php echo $musteriler[0]->bakiye." ₺"; ?></p>
										</td>
										<td>
											<p><?php if ($sorgu) {
												echo $sorgu[0]->personel_id; 
											}?></p>
										</td>
										<td>
											<p><?php if ($sorgu) {
												echo $sorgu[0]->date_time; 
											}?></p>
										</td>
										<td><a href="<?php echo base_url('admin/barkodyazdir/'.$musteriler[0]->kart_id.''); ?>" class="btn btn btn-success"><i class="fas fa-print"></i> Tekrar Yazdır</a>
										</td>
									</tr>
									<?php } ?>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<?php $this->load->view('admin/include/footer'); ?>