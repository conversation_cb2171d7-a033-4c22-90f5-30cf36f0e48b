<?php 	
$host = "localhost";
$db_user = "root";
$db_pass = "";
$backup_dir = "C:/xampp/htdocs/petrolsistemi/yedekler/";
$db_names = "petrolsistemi";
$connn = new mysqli($host, $db_user, $db_pass, $db_names);
$servername = "***********";
$username = "u411458722_ulaopet";
$password = "I@HjABh5u#";
$dbname = "u411458722_ulaopet";
$conn = new mysqli($servername, $username, $password, $dbname);
if ($conn->connect_error) {
	die("Bağlantı hatası: " . $conn->connect_error);
}
$checkTableQuery = "SELECT COUNT(*) as rowCount FROM mustericlone";
$result = $conn->query($checkTableQuery);
if ($result) {
	$row = $result->fetch_assoc();
	$rowCount = $row["rowCount"];

	if ($rowCount > 0) {
        // mustericlone tablosunda veri var, i<PERSON><PERSON>i yapma
        //echo "Online mustericlone tablosunda zaten veri var, işlem yapılmayacak.";
	} else {
		$sorgu = "SELECT * FROM mustericlone ORDER BY id ASC";
		$source_result = mysqli_query($connn, $sorgu);
		while ($row = mysqli_fetch_assoc($source_result)) {
			$kart_id = $row["kart_id"];
			$name = $row["plaka"];
			$telefon = $row["telefon"];
			$bakiye=["bakiye"];
			$barkod_personel =$row["barkod_personel"];
			$islem=$row["islem"];
			$insertquery = "INSERT INTO mustericlone (kart_id, name, plaka, telefon, bakiye, barkod_personel, islem)
			VALUES ('$kart_id', '$name', '$plaka', '$telefon', '$bakiye', '$barkod_personel', '$islem')";
			$target_result = mysqli_query($conn, $insertquery);
		}
		$denemequery = "TRUNCATE TABLE mustericlone";
		$denemequerycalistir = mysqli_query($connn, $denemequery);
	}
}
?>