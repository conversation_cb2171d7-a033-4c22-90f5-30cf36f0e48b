@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

.wrapperr {
    padding: 10px 10px;
    
    
    border-radius: 3px;
    
    width: 100%;
    

}

.wrapperr .titlee {
    font-weight: 800;  
    font-size: 2rem;
    color: #111;
    
}

.wrapperr .inputFieldss {
    right: 1000px;
    margin: 25px 0;
    display: flex;
    justify-content: space-between;
        height: 50px;
}

.wrapperr .inputFieldss input[type="text"] {
    
    background: white;


    border: 1px solid rgba(0,0,0,.3);
    outline: none;
   
    width: calc(100% - 60px);
    height: 100%;
    border-radius: 3px;
    padding: 0 10px;
    font-size: 1rem;
    transition: .3s;
}

.wrapperr .inputFieldss input[type="text"]:focus {
    border-color: #6c5ce7;
    right: 1000px;
}

.wrapperr .inputFieldss button.btn {
    width: 50px;
    height: 100%;
    background: #6c5ce7;
    color: #FFF;
    outline: none;
    border: none;
    box-shadow: 0 0 5px rgba(0,0,0,.1);
    border-radius: 3px;
    font-size: 1rem;
    cursor: pointer;
    opacity: .8;
    transition: .3s;
}

.wrapperr .inputFieldss button.btn:hover {
    opacity: 1;
}

.wrapperr .todo ul li {

    background: white;
    border-radius: 8px;

    right: 50px;
    margin: 8px 0;
    padding: 10px 0;
    border: 1px solid rgba(0,0,0,.1);
    display: felx;
    justify-content: space-between;
    align-items: center;
    font-size: 1rem;
    position: relative;


    overflow: hidden;
}

.wrapperr .todo ul li .text {
    background: darkgray;
    color: #111;
    padding-left: 10px;
    justify-content: center;
}

.wrapperr .todo ul li .icon {
    width: 50px;
    height: 100%;
    background: #e74c3c;
    position: absolute;
    right: -50px;
    top: 0;
    color: #FFF;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 0 3px 3px 0;
    transition: .3s linear;
}

.wrapperr .todo ul li:hover .icon {
    right: 0;
}

.wrapperr .todo .pending-text {
    color: #111;
    font-weight: 500;
    font-size: 1rem;
    margin-top: 20px;
    text-transform: capitalize;
}