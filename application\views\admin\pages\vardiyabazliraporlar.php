<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<style type="text/css">
	hr {
		border: 7px solid red;
		border-radius: 5px;
	}
</style>
<?php echo $counter; ?>
<section class="content">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xl-12">
				<div class="box box-solid">
					<div class="box-body">
						<?php 
						$vardiya1toplam = 0;
						$vardiya2toplam = 0;
						$vardiya3toplam = 0;
						$vardiya4toplam = 0;
						$vardiya5toplam = 0;
						$vardiya6toplam = 0;
						$vardiyatoplam = 0;
						$vardiya1promosyon = 0;
						$vardiya2promosyon = 0;
						$vardiya3promosyon = 0;
						$vardiya4promosyon = 0;
						$vardiya5promosyon = 0;
						$vardiya6promosyon = 0;
						$vardiyalarpromosyontoplam = 0;
						$vardiya1bakiyeiadetoplam = 0;
						$vardiya2bakiyeiadetoplam = 0;
						$vardiya3bakiyeiadetoplam = 0;
						$vardiya4bakiyeiadetoplam = 0;
						$vardiya5bakiyeiadetoplam = 0;
						$vardiya6bakiyeiadetoplam = 0;
						$vardiyabakiyeiadetoplam = 0;
						$vardiya1depozitoiadetoplam = 0;
						$vardiya2depozitoiadetoplam = 0;
						$vardiya3depozitoiadetoplam = 0;
						$vardiya4depozitoiadetoplam = 0;
						$vardiya5depozitoiadetoplam = 0;
						$vardiya6depozitoiadetoplam = 0;
						$vardiyadepozitoiadetoplam = 0;
						$vardiya1depozitotoplam = 0;
						$vardiya2depozitotoplam = 0;
						$vardiya3depozitotoplam = 0;
						$vardiya4depozitotoplam = 0;
						$vardiya5depozitotoplam = 0;
						$vardiya6depozitotoplam = 0;
						$vardiyadepozitotoplam = 0;
						?>
						<?php if ($counter == 1) {?>
							<b><label>1. VARDİYA<?php echo $counter; ?></label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar1 as $vardiya1){ 
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya1->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>1. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor1 as $iaderapor1){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor1->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>VARDİYALAR TOPLAMI</label></b>
							<div class=" row ">
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>1.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1.Vardiya Toplamı</td>
											<?php foreach ($raporlar1bakiye as $raporlar1bakiye) {
												$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
											} ?>
											<td><?php echo $vardiya1toplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor1) {
												foreach ($depozitorapor1 as $depozitorapor1) {
													$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
												}
											} ?>
											<td><?php echo $vardiya1depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar1promosyon) {

													foreach ($raporlar1promosyon as $raporlar1promosyon) {
														$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
													}
													echo $vardiya1promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor11) {

												foreach ($iaderapor11 as $iaderapor1) {
													$toplanacakbakiye = $iaderapor1->iade_bakiye;
													$toplanacakdepozito = $iaderapor1->iade_depozito;
													$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya1bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya1depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>GENEL TOPLAM</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>Vardiyalar Genel Toplamı</td>
											<?php 
											$vardiyatoplam = $vardiyatoplam + $vardiya1toplam;
											?>
											<td><?php echo $vardiyatoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Depozitolar Genel Toplamı</td>
											<?php 
											$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam;
											?>
											<td><?php echo $vardiyadepozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Promosyonlar Genel Toplamı</td>
											<?php $vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon; ?>
											<td><?php echo $vardiyalarpromosyontoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Bakiye İadeleri Genel Toplamı</td>
											<?php 
											$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam;
											?>
											<td><?php echo $vardiyabakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Depozito İadeleri Genel Toplamı</td>
											<?php 
											$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam;
											?>
											<td><?php echo $vardiyadepozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
							</div>
						<?php } ?>

						<?php if ($counter == 2) {?>
							<b><label>1. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar1 as $vardiya1){ 
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya1->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>1. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor1 as $iaderapor1){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor1->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>2. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar2 as $vardiya2){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya2->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>2. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor2 as $iaderapor2){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor2->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>VARDİYALAR TOPLAMI</label></b>
							<div class=" row ">
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>1.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1.Vardiya Toplamı</td>
											<?php foreach ($raporlar1bakiye as $raporlar1bakiye) {
												$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
											} ?>
											<td><?php echo $vardiya1toplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor1) {
												foreach ($depozitorapor1 as $depozitorapor1) {
													$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
												}
											} ?>
											<td><?php echo $vardiya1depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar1promosyon) {

													foreach ($raporlar1promosyon as $raporlar1promosyon) {
														$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
													}
													echo $vardiya1promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor11) {

												foreach ($iaderapor11 as $iaderapor1) {
													$toplanacakbakiye = $iaderapor1->iade_bakiye;
													$toplanacakdepozito = $iaderapor1->iade_depozito;
													$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya1bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya1depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>

										<th colspan="2"><center>2.VARDİYA</center></th>


									</thead>
									<tbody>
										<tr>
											<td>2.Vardiya Toplamı</td>
											<?php foreach ($raporlar2bakiye as $raporlar2bakiye) {
												$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya2toplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor2) {
												foreach ($depozitorapor2 as $depozitorapor2) {
													$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
												}
											} ?>
											<td><?php echo $vardiya2depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar2promosyon) {

													foreach ($raporlar2promosyon as $raporlar2promosyon) {
														$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
													}
													echo $vardiya2promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor22) {

												foreach ($iaderapor22 as $iaderapor2) {
													$toplanacakbakiye = $iaderapor2->iade_bakiye;
													$toplanacakdepozito = $iaderapor2->iade_depozito;
													$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya2bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya2depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>GENEL TOPLAM</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>Vardiyalar Genel Toplamı</td>
											<?php 
											$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam;
											?>
											<td><?php echo $vardiyatoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Depozitolar Genel Toplamı</td>
											<?php 
											$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam;
											?>
											<td><?php echo $vardiyadepozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Promosyonlar Genel Toplamı</td>
											<?php $vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon; ?>
											<td><?php echo $vardiyalarpromosyontoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Bakiye İadeleri Genel Toplamı</td>
											<?php 
											$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam;
											?>
											<td><?php echo $vardiyabakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Depozito İadeleri Genel Toplamı</td>
											<?php 
											$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam;
											?>
											<td><?php echo $vardiyadepozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
							</div>
						<?php } ?>
						<?php if ($counter == 3) {?>
							<b><label>1. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar1 as $vardiya1){ 
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya1->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>1. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor1 as $iaderapor1){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor1->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>2. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar2 as $vardiya2){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya2->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>2. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor2 as $iaderapor2){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor2->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>3. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar3 as $vardiya3){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya3->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>3. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor3 as $iaderapor3){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor3->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>VARDİYALAR TOPLAMI</label></b>
							<div class=" row ">
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>1.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1.Vardiya Toplamı</td>
											<?php foreach ($raporlar1bakiye as $raporlar1bakiye) {
												$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
											} ?>
											<td><?php echo $vardiya1toplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor1) {
												foreach ($depozitorapor1 as $depozitorapor1) {
													$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
												}
											} ?>
											<td><?php echo $vardiya1depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar1promosyon) {

													foreach ($raporlar1promosyon as $raporlar1promosyon) {
														$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
													}
													echo $vardiya1promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor11) {

												foreach ($iaderapor11 as $iaderapor1) {
													$toplanacakbakiye = $iaderapor1->iade_bakiye;
													$toplanacakdepozito = $iaderapor1->iade_depozito;
													$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya1bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya1depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>

										<th colspan="2"><center>2.VARDİYA</center></th>


									</thead>
									<tbody>
										<tr>
											<td>2.Vardiya Toplamı</td>
											<?php foreach ($raporlar2bakiye as $raporlar2bakiye) {
												$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya2toplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor2) {
												foreach ($depozitorapor2 as $depozitorapor2) {
													$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
												}
											} ?>
											<td><?php echo $vardiya2depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar2promosyon) {

													foreach ($raporlar2promosyon as $raporlar2promosyon) {
														$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
													}
													echo $vardiya2promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor22) {

												foreach ($iaderapor22 as $iaderapor2) {
													$toplanacakbakiye = $iaderapor2->iade_bakiye;
													$toplanacakdepozito = $iaderapor2->iade_depozito;
													$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya2bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya2depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>3.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>3.Vardiya Toplamı</td>
											<?php foreach ($raporlar3bakiye as $raporlar3bakiye) {
												$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya3toplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor3) {
												foreach ($depozitorapor3 as $depozitorapor3) {
													$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
												}
											} ?>
											<td><?php echo $vardiya3depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar3promosyon) {

													foreach ($raporlar3promosyon as $raporlar3promosyon) {
														$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
													}
													echo $vardiya3promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor33) {

												foreach ($iaderapor33 as $iaderapor3) {
													$toplanacakbakiye = $iaderapor3->iade_bakiye;
													$toplanacakdepozito = $iaderapor3->iade_depozito;
													$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya3bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya3depozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>GENEL TOPLAM</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>Vardiyalar Genel Toplamı</td>
											<?php 
											$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam;
											?>
											<td><?php echo $vardiyatoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Depozitolar Genel Toplamı</td>
											<?php 
											$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam;
											?>
											<td><?php echo $vardiyadepozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Promosyonlar Genel Toplamı</td>
											<?php $vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon; ?>
											<td><?php echo $vardiyalarpromosyontoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Bakiye İadeleri Genel Toplamı</td>
											<?php 
											$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam;
											?>
											<td><?php echo $vardiyabakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Depozito İadeleri Genel Toplamı</td>
											<?php 
											$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam;
											?>
											<td><?php echo $vardiyadepozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
							</div>

						<?php } ?>
						<?php if ($counter == 4) {?>
							<b><label>1. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar1 as $vardiya1){ 
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya1->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>1. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor1 as $iaderapor1){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor1->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>2. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar2 as $vardiya2){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya2->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>2. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor2 as $iaderapor2){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor2->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>3. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar3 as $vardiya3){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya3->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>3. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor3 as $iaderapor3){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor3->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>4. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar4 as $vardiya4){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya4->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya4->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya4->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya4->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya4->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<b><label>4. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor4 as $iaderapor4){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor4->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor4->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor4->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor4->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor4->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor4->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>VARDİYALAR TOPLAMI</label></b>
							<div class=" row ">
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>1.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1.Vardiya Toplamı</td>
											<?php foreach ($raporlar1bakiye as $raporlar1bakiye) {
												$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
											} ?>
											<td><?php echo $vardiya1toplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor1) {
												foreach ($depozitorapor1 as $depozitorapor1) {
													$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
												}
											} ?>
											<td><?php echo $vardiya1depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar1promosyon) {

													foreach ($raporlar1promosyon as $raporlar1promosyon) {
														$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
													}
													echo $vardiya1promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor11) {

												foreach ($iaderapor11 as $iaderapor1) {
													$toplanacakbakiye = $iaderapor1->iade_bakiye;
													$toplanacakdepozito = $iaderapor1->iade_depozito;
													$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya1bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya1depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>

										<th colspan="2"><center>2.VARDİYA</center></th>


									</thead>
									<tbody>
										<tr>
											<td>2.Vardiya Toplamı</td>
											<?php foreach ($raporlar2bakiye as $raporlar2bakiye) {
												$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya2toplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor2) {
												foreach ($depozitorapor2 as $depozitorapor2) {
													$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
												}
											} ?>
											<td><?php echo $vardiya2depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar2promosyon) {

													foreach ($raporlar2promosyon as $raporlar2promosyon) {
														$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
													}
													echo $vardiya2promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor22) {

												foreach ($iaderapor22 as $iaderapor2) {
													$toplanacakbakiye = $iaderapor2->iade_bakiye;
													$toplanacakdepozito = $iaderapor2->iade_depozito;
													$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya2bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya2depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>3.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>3.Vardiya Toplamı</td>
											<?php foreach ($raporlar3bakiye as $raporlar3bakiye) {
												$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya3toplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor3) {
												foreach ($depozitorapor3 as $depozitorapor3) {
													$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
												}
											} ?>
											<td><?php echo $vardiya3depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar3promosyon) {

													foreach ($raporlar3promosyon as $raporlar3promosyon) {
														$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
													}
													echo $vardiya3promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor33) {

												foreach ($iaderapor33 as $iaderapor3) {
													$toplanacakbakiye = $iaderapor3->iade_bakiye;
													$toplanacakdepozito = $iaderapor3->iade_depozito;
													$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya3bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya3depozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>4.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>4.Vardiya Toplamı</td>
											<?php foreach ($raporlar4bakiye as $raporlar4bakiye) {
												$vardiya4toplam = $vardiya4toplam + $raporlar4bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya4toplam; ?>₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor4) {
												foreach ($depozitorapor4 as $depozitorapor4) {
													$vardiya4depozitotoplam = $vardiya4depozitotoplam + $depozitorapor4->depozito;
												}
											} ?>
											<td><?php echo $vardiya4depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>4. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar4promosyon) {

													foreach ($raporlar4promosyon as $raporlar4promosyon) {
														$vardiya4promosyon = $vardiya4promosyon + $raporlar4promosyon->eklenecek_bakiye;
													}
													echo $vardiya4promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor44) {

												foreach ($iaderapor44 as $iaderapor4) {
													$toplanacakbakiye = $iaderapor4->iade_bakiye;
													$toplanacakdepozito = $iaderapor4->iade_depozito;
													$vardiya4bakiyeiadetoplam = $vardiya4bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya4depozitoiadetoplam = $vardiya4depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya4bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya4depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>GENEL TOPLAM</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>Vardiyalar Genel Toplamı</td>
											<?php 
											$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam + $vardiya4toplam;
											?>
											<td><?php echo $vardiyatoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Depozitolar Genel Toplamı</td>
											<?php 
											$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam + $vardiya4depozitotoplam;
											?>
											<td><?php echo $vardiyadepozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Promosyonlar Genel Toplamı</td>
											<?php $vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon + $vardiya4promosyon; ?>
											<td><?php echo $vardiyalarpromosyontoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Bakiye İadeleri Genel Toplamı</td>
											<?php 
											$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam + $vardiya4bakiyeiadetoplam;
											?>
											<td><?php echo $vardiyabakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Depozito İadeleri Genel Toplamı</td>
											<?php 
											$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam + $vardiya4depozitoiadetoplam;
											?>
											<td><?php echo $vardiyadepozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
							</div>

						<?php } ?>
						<?php if ($counter == 5) {?>
							<b><label>1. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar1 as $vardiya1){ 
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya1->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>1. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor1 as $iaderapor1){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor1->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>2. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar2 as $vardiya2){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya2->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>2. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor2 as $iaderapor2){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor2->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>3. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar3 as $vardiya3){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya3->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>3. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor3 as $iaderapor3){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor3->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>4. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar4 as $vardiya4){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya4->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya4->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya4->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya4->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya4->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<b><label>4. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor4 as $iaderapor4){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor4->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor4->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor4->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor4->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor4->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor4->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>5. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar5 as $vardiya5){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya5->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya5->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya5->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya5->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya5->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<b><label>5. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor5 as $iaderapor5){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor5->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor5->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor5->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor5->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor5->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor5->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>VARDİYALAR TOPLAMI</label></b>
							<div class=" row ">
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>1.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1.Vardiya Toplamı</td>
											<?php foreach ($raporlar1bakiye as $raporlar1bakiye) {
												$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
											} ?>
											<td><?php echo $vardiya1toplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor1) {
												foreach ($depozitorapor1 as $depozitorapor1) {
													$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
												}
											} ?>
											<td><?php echo $vardiya1depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar1promosyon) {

													foreach ($raporlar1promosyon as $raporlar1promosyon) {
														$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
													}
													echo $vardiya1promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor11) {

												foreach ($iaderapor11 as $iaderapor1) {
													$toplanacakbakiye = $iaderapor1->iade_bakiye;
													$toplanacakdepozito = $iaderapor1->iade_depozito;
													$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya1bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya1depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>

										<th colspan="2"><center>2.VARDİYA</center></th>


									</thead>
									<tbody>
										<tr>
											<td>2.Vardiya Toplamı</td>
											<?php foreach ($raporlar2bakiye as $raporlar2bakiye) {
												$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya2toplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor2) {
												foreach ($depozitorapor2 as $depozitorapor2) {
													$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
												}
											} ?>
											<td><?php echo $vardiya2depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar2promosyon) {

													foreach ($raporlar2promosyon as $raporlar2promosyon) {
														$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
													}
													echo $vardiya2promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor22) {

												foreach ($iaderapor22 as $iaderapor2) {
													$toplanacakbakiye = $iaderapor2->iade_bakiye;
													$toplanacakdepozito = $iaderapor2->iade_depozito;
													$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya2bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya2depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>3.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>3.Vardiya Toplamı</td>
											<?php foreach ($raporlar3bakiye as $raporlar3bakiye) {
												$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya3toplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor3) {
												foreach ($depozitorapor3 as $depozitorapor3) {
													$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
												}
											} ?>
											<td><?php echo $vardiya3depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar3promosyon) {

													foreach ($raporlar3promosyon as $raporlar3promosyon) {
														$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
													}
													echo $vardiya3promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor33) {

												foreach ($iaderapor33 as $iaderapor3) {
													$toplanacakbakiye = $iaderapor3->iade_bakiye;
													$toplanacakdepozito = $iaderapor3->iade_depozito;
													$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya3bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya3depozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>4.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>4.Vardiya Toplamı</td>
											<?php foreach ($raporlar4bakiye as $raporlar4bakiye) {
												$vardiya4toplam = $vardiya4toplam + $raporlar4bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya4toplam; ?>₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor4) {
												foreach ($depozitorapor4 as $depozitorapor4) {
													$vardiya4depozitotoplam = $vardiya4depozitotoplam + $depozitorapor4->depozito;
												}
											} ?>
											<td><?php echo $vardiya4depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>4. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar4promosyon) {

													foreach ($raporlar4promosyon as $raporlar4promosyon) {
														$vardiya4promosyon = $vardiya4promosyon + $raporlar4promosyon->eklenecek_bakiye;
													}
													echo $vardiya4promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor44) {

												foreach ($iaderapor44 as $iaderapor4) {
													$toplanacakbakiye = $iaderapor4->iade_bakiye;
													$toplanacakdepozito = $iaderapor4->iade_depozito;
													$vardiya4bakiyeiadetoplam = $vardiya4bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya4depozitoiadetoplam = $vardiya4depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya4bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya4depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>5.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>5.Vardiya Toplamı</td>
											<?php foreach ($raporlar5bakiye as $raporlar5bakiye) {
												$vardiya5toplam = $vardiya5toplam + $raporlar5bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya5toplam; ?>₺</td>
										</tr>
										<tr>
											<td>5.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor5) {
												foreach ($depozitorapor5 as $depozitorapor5) {
													$vardiya5depozitotoplam = $vardiya5depozitotoplam + $depozitorapor5->depozito;
												}
											} ?>
											<td><?php echo $vardiya5depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>5. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar5promosyon) {

													foreach ($raporlar5promosyon as $raporlar5promosyon) {
														$vardiya5promosyon = $vardiya5promosyon + $raporlar5promosyon->eklenecek_bakiye;
													}
													echo $vardiya5promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>5.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor55) {

												foreach ($iaderapor55 as $iaderapor5) {
													$toplanacakbakiye = $iaderapor5->iade_bakiye;
													$toplanacakdepozito = $iaderapor5->iade_depozito;
													$vardiya5bakiyeiadetoplam = $vardiya5bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya5depozitoiadetoplam = $vardiya5depozitoiadetoplam + $toplanacakdepozito; 
												}
											}

											?>
											<td><?php echo $vardiya5bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>5.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya5depozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>GENEL TOPLAM</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>Vardiyalar Genel Toplamı</td>
											<?php 
											$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam + $vardiya4toplam + $vardiya5toplam;
											?>
											<td><?php echo $vardiyatoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Depozitolar Genel Toplamı</td>
											<?php 
											$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam + $vardiya4depozitotoplam + $vardiya5depozitotoplam;
											?>
											<td><?php echo $vardiyadepozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Promosyonlar Genel Toplamı</td>
											<?php $vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon + $vardiya4promosyon + $vardiya5promosyon; ?>
											<td><?php echo $vardiyalarpromosyontoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Bakiye İadeleri Genel Toplamı</td>
											<?php 
											$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam + $vardiya4bakiyeiadetoplam + $vardiya5bakiyeiadetoplam;
											?>
											<td><?php echo $vardiyabakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Depozito İadeleri Genel Toplamı</td>
											<?php 
											$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam + $vardiya4depozitoiadetoplam + $vardiya5depozitoiadetoplam;
											?>
											<td><?php echo $vardiyadepozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
							</div>
						<?php } ?>
						<?php if ($counter == 6) {?>
							<b><label>1. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar1 as $vardiya1){ 
										?>
										<tr>
											<td>
												<p><?php echo $vardiya1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya1->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya1->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>1. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor1 as $iaderapor1){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor1->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor1->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor1->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>2. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar2 as $vardiya2){
										?>
										<tr>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor2->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>

											</td>
											<td>
												<p><?php echo $vardiya2->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya2->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>2. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor2 as $iaderapor2){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor2->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor2->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor2->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>3. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar3 as $vardiya3){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya3->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya3->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>
								</tbody>
							</table>
							<b><label>3. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor3 as $iaderapor3){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor3->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor3->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor3->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>4. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar4 as $vardiya4){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya4->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya4->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya4->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya4->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya4->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<b><label>4. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor4 as $iaderapor4){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor4->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor4->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor4->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor4->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor4->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor4->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>5. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar5 as $vardiya5){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya5->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya5->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya5->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya5->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya5->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<b><label>5. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor5 as $iaderapor5){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor5->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor5->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor5->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor5->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor5->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor5->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>6. VARDİYA</label></b>
							<table  class="table table-bordered table-striped" style="width:100%">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>Yükleyen Personel</th>
										<th>Eklenen Bakiye</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($raporlar6 as $vardiya6){
										?>
										<tr>
											<td>
												<p><?php echo $vardiya6->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu1 = musteri::select(['kart_id'=>$vardiya6->kart_id]);
												if ($sorgu1) {
													echo $sorgu1[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $vardiya6->personel_id; ?></p>
											</td>
											<td>
												<p><?php echo $vardiya6->eklenecek_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $vardiya6->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<b><label>6. VARDİYA İADELER TABLOSU</label></b>
							<table  class="table table-bordered table-striped" style="width: 100%;">
								<thead>
									<tr>
										<th>Kart ID</th>
										<th>Isim</th>
										<th>İade Eden Personel</th>
										<th>İade Edilen Bakiye</th>
										<th>İade Edilen depozito</th>
										<th>Tarih</th>

									</tr>
								</thead>
								<tbody>
									<?php foreach ($iaderapor6 as $iaderapor6){
										?>
										<tr>
											<td>
												<p><?php echo $iaderapor6->kart_id; ?></p>
											</td>
											<td>
												<p><?php 
												$sorgu2 = musteri::select(['kart_id'=>$iaderapor6->kart_id]);
												if ($sorgu2) {
													echo $sorgu2[0]->name;
												}else{
													echo "Kart Iade Edildi";
												}?></p>
											</td>
											<td>
												<p><?php echo $iaderapor6->personel; ?></p>
											</td>
											<td>
												<p><?php echo $iaderapor6->iade_bakiye; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor6->iade_depozito; ?>₺</p>
											</td>
											<td>
												<p><?php echo $iaderapor6->date_time; ?></p>
											</td>
										</tr>
									<?php } ?>

								</tbody>
							</table>
							<br><hr>
							<b><label>VARDİYALAR TOPLAMI</label></b>
							<div class=" row ">
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>1.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>1.Vardiya Toplamı</td>
											<?php foreach ($raporlar1bakiye as $raporlar1bakiye) {
												$vardiya1toplam = $vardiya1toplam + $raporlar1bakiye->eklenecek_bakiye;
											} ?>
											<td><?php echo $vardiya1toplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor1) {
												foreach ($depozitorapor1 as $depozitorapor1) {
													$vardiya1depozitotoplam = $vardiya1depozitotoplam + $depozitorapor1->depozito;
												}
											} ?>
											<td><?php echo $vardiya1depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar1promosyon) {

													foreach ($raporlar1promosyon as $raporlar1promosyon) {
														$vardiya1promosyon = $vardiya1promosyon + $raporlar1promosyon->eklenecek_bakiye;
													}
													echo $vardiya1promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor11) {

												foreach ($iaderapor11 as $iaderapor1) {
													$toplanacakbakiye = $iaderapor1->iade_bakiye;
													$toplanacakdepozito = $iaderapor1->iade_depozito;
													$vardiya1bakiyeiadetoplam = $vardiya1bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya1depozitoiadetoplam = $vardiya1depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya1bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>1.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya1depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>

										<th colspan="2"><center>2.VARDİYA</center></th>


									</thead>
									<tbody>
										<tr>
											<td>2.Vardiya Toplamı</td>
											<?php foreach ($raporlar2bakiye as $raporlar2bakiye) {
												$vardiya2toplam = $vardiya2toplam + $raporlar2bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya2toplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor2) {
												foreach ($depozitorapor2 as $depozitorapor2) {
													$vardiya2depozitotoplam = $vardiya2depozitotoplam + $depozitorapor2->depozito;
												}
											} ?>
											<td><?php echo $vardiya2depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar2promosyon) {

													foreach ($raporlar2promosyon as $raporlar2promosyon) {
														$vardiya2promosyon = $vardiya2promosyon + $raporlar2promosyon->eklenecek_bakiye;
													}
													echo $vardiya2promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor22) {

												foreach ($iaderapor22 as $iaderapor2) {
													$toplanacakbakiye = $iaderapor2->iade_bakiye;
													$toplanacakdepozito = $iaderapor2->iade_depozito;
													$vardiya2bakiyeiadetoplam = $vardiya2bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya2depozitoiadetoplam = $vardiya2depozitoiadetoplam + $toplanacakdepozito;
												}
											}

											?>
											<td><?php echo $vardiya2bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>2.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya2depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>3.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>3.Vardiya Toplamı</td>
											<?php foreach ($raporlar3bakiye as $raporlar3bakiye) {
												$vardiya3toplam = $vardiya3toplam + $raporlar3bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya3toplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor3) {
												foreach ($depozitorapor3 as $depozitorapor3) {
													$vardiya3depozitotoplam = $vardiya3depozitotoplam + $depozitorapor3->depozito;
												}
											} ?>
											<td><?php echo $vardiya3depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar3promosyon) {

													foreach ($raporlar3promosyon as $raporlar3promosyon) {
														$vardiya3promosyon = $vardiya3promosyon + $raporlar3promosyon->eklenecek_bakiye;
													}
													echo $vardiya3promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor33) {

												foreach ($iaderapor33 as $iaderapor3) {
													$toplanacakbakiye = $iaderapor3->iade_bakiye;
													$toplanacakdepozito = $iaderapor3->iade_depozito;
													$vardiya3bakiyeiadetoplam = $vardiya3bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya3depozitoiadetoplam = $vardiya3depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya3bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>3.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya3depozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>4.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>4.Vardiya Toplamı</td>
											<?php foreach ($raporlar4bakiye as $raporlar4bakiye) {
												$vardiya4toplam = $vardiya4toplam + $raporlar4bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya4toplam; ?>₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor4) {
												foreach ($depozitorapor4 as $depozitorapor4) {
													$vardiya4depozitotoplam = $vardiya4depozitotoplam + $depozitorapor4->depozito;
												}
											} ?>
											<td><?php echo $vardiya4depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>4. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar4promosyon) {

													foreach ($raporlar4promosyon as $raporlar4promosyon) {
														$vardiya4promosyon = $vardiya4promosyon + $raporlar4promosyon->eklenecek_bakiye;
													}
													echo $vardiya4promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor44) {

												foreach ($iaderapor44 as $iaderapor4) {
													$toplanacakbakiye = $iaderapor4->iade_bakiye;
													$toplanacakdepozito = $iaderapor4->iade_depozito;
													$vardiya4bakiyeiadetoplam = $vardiya4bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya4depozitoiadetoplam = $vardiya4depozitoiadetoplam + $toplanacakdepozito; 
												}

											}
											?>
											<td><?php echo $vardiya4bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>4.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya4depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>5.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>5.Vardiya Toplamı</td>
											<?php foreach ($raporlar5bakiye as $raporlar5bakiye) {
												$vardiya5toplam = $vardiya5toplam + $raporlar5bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<td><?php echo $vardiya5toplam; ?>₺</td>
										</tr>
										<tr>
											<td>5.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor5) {
												foreach ($depozitorapor5 as $depozitorapor5) {
													$vardiya5depozitotoplam = $vardiya5depozitotoplam + $depozitorapor5->depozito;
												}
											} ?>
											<td><?php echo $vardiya5depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>5. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar5promosyon) {

													foreach ($raporlar5promosyon as $raporlar5promosyon) {
														$vardiya5promosyon = $vardiya5promosyon + $raporlar5promosyon->eklenecek_bakiye;
													}
													echo $vardiya5promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>5.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor55) {

												foreach ($iaderapor55 as $iaderapor5) {
													$toplanacakbakiye = $iaderapor5->iade_bakiye;
													$toplanacakdepozito = $iaderapor5->iade_depozito;
													$vardiya5bakiyeiadetoplam = $vardiya5bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya5depozitoiadetoplam = $vardiya5depozitoiadetoplam + $toplanacakdepozito; 
												}
											}

											?>
											<td><?php echo $vardiya5bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>5.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya5depozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>6.VARDİYA</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>6.Vardiya Toplamı</td>
											<?php foreach ($raporlar6bakiye as $raporlar6bakiye) {
												$vardiya6toplam = $vardiya6toplam + $raporlar6bakiye->eklenecek_bakiye;
												$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											} ?>
											<?php 
											$vardiyatoplam = $vardiya1toplam + $vardiya2toplam + $vardiya3toplam+$vardiya4toplam+$vardiya5toplam+$vardiya6toplam;
											$geneltoplam = $vardiyatoplam + $depozito_toplam ;
											?>
											<td><?php echo $vardiya6toplam; ?>₺</td>
										</tr>
										<tr>
											<td>6.Vardiya Depozito Toplamı</td>
											<?php if ($depozitorapor6) {
												foreach ($depozitorapor6 as $depozitorapor6) {
													$vardiya6depozitotoplam = $vardiya6depozitotoplam + $depozitorapor6->depozito;
												}
											} ?>
											<td><?php echo $vardiya6depozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>6. Vardiya Promosyon Toplamı</td>
											<td>
												<?php if ($raporlar6promosyon) {

													foreach ($raporlar6promosyon as $raporlar6promosyon) {
														$vardiya6promosyon = $vardiya6promosyon + $raporlar6promosyon->eklenecek_bakiye;
													}
													echo $vardiya6promosyon;

												}else{
													echo "0";
												}
												?>
											₺</td>
										</tr>
										<tr>
											<td>6.Vardiya Kart Bakiye İadesi</td>
											<?php  if ($iaderapor66) {

												foreach ($iaderapor66 as $iaderapor6) {
													$toplanacakbakiye = $iaderapor6->iade_bakiye;
													$toplanacakdepozito = $iaderapor6->iade_depozito;
													$vardiya6bakiyeiadetoplam = $vardiya6bakiyeiadetoplam + $toplanacakbakiye;
													$vardiya6depozitoiadetoplam = $vardiya6depozitoiadetoplam + $toplanacakdepozito; 
												}
											}

											?>
											<td><?php echo $vardiya6bakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>6.Vardiya Kart Depozito İadesi</td>
											<td><?php echo $vardiya6depozitoiadetoplam; ?>₺</td>
										</tr>

									</tbody>
								</table>
								&nbsp;&nbsp;&nbsp;
								<table  class="table table-bordered table-striped col-md-2" >
									<thead>
										<tr>
											<th colspan="2"><center>GENEL TOPLAM</center></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td>Vardiyalar Genel Toplamı</td>
											<?php 
											$vardiyatoplam = $vardiyatoplam + $vardiya1toplam + $vardiya2toplam + $vardiya3toplam + $vardiya4toplam + $vardiya5toplam + $vardiya6toplam;
											?>
											<td><?php echo $vardiyatoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Depozitolar Genel Toplamı</td>
											<?php 
											$vardiyadepozitotoplam = $vardiyadepozitotoplam + $vardiya1depozitotoplam + $vardiya2depozitotoplam + $vardiya3depozitotoplam + $vardiya4depozitotoplam + $vardiya5depozitotoplam + $vardiya6depozitotoplam;
											?>
											<td><?php echo $vardiyadepozitotoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Promosyonlar Genel Toplamı</td>
											<?php $vardiyalarpromosyontoplam = $vardiyalarpromosyontoplam + $vardiya1promosyon + $vardiya2promosyon + $vardiya3promosyon + $vardiya4promosyon + $vardiya5promosyon + $vardiya6promosyon; ?>
											<td><?php echo $vardiyalarpromosyontoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Bakiye İadeleri Genel Toplamı</td>
											<?php 
											$vardiyabakiyeiadetoplam = $vardiyabakiyeiadetoplam + $vardiya1bakiyeiadetoplam + $vardiya2bakiyeiadetoplam + $vardiya3bakiyeiadetoplam + $vardiya4bakiyeiadetoplam + $vardiya5bakiyeiadetoplam + $vardiya6bakiyeiadetoplam;
											?>
											<td><?php echo $vardiyabakiyeiadetoplam; ?>₺</td>
										</tr>
										<tr>
											<td>Kart Depozito İadeleri Genel Toplamı</td>
											<?php 
											$vardiyadepozitoiadetoplam = $vardiyadepozitoiadetoplam + $vardiya1depozitoiadetoplam + $vardiya2depozitoiadetoplam + $vardiya3depozitoiadetoplam + $vardiya4depozitoiadetoplam + $vardiya5depozitoiadetoplam + $vardiya6depozitoiadetoplam;
											?>
											<td><?php echo $vardiyadepozitoiadetoplam; ?>₺</td>
										</tr>
									</tbody>
								</table>
							</div>
						<?php } ?>

					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<?php $this->load->view('admin/include/footer'); ?>