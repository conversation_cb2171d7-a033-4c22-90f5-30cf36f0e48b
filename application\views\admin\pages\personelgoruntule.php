<?php $this->load->view('admin/include/header'); ?>
<?php $this->load->view('admin/include/leftmenu'); ?>
<section class="content">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xl-12">
				<div class="box box-solid">
					<div class="box-body">
						<table id="personelpetrol" class="table table-bordered table-striped" style="width:100%">
							<thead>
								<tr>
									<th>İsim</th>
									<th>E-Mail</th>
									<th>Şifre</th>
									<th>Yetki</th>
									<th><PERSON>şlemler</th>
								</tr>
							</thead>
							<tbody>
								<?php foreach ($personeller as $personel) 
								{ ?>
									<?php if ($personel->is_active != 0 AND $personel->role != "Supervisor"){ ?>
										<tr>
											<td>
												<p><?php echo $personel->name; ?></p>
											</td>
											<td>
												<p><?php echo $personel->email; ?></p>
											</td>
											<td>
												<p><?php echo $personel->password; ?></p>
											</td>
											<td>
												<p><?php echo $personel->role; ?></p>
											</td>
											<td>
												<button type="button" class="btn btn-danger" title="Personel sil" data-toggle="modal" data-target="#modal-danger-<?php echo $personel->id; ?>"><i class="fas fa-trash"></i></button>
												<div class="modal fade" id="modal-danger-<?php echo $personel->id; ?>">
													<div class="modal-dialog">
														<div class="modal-content">
															<div class="modal-header">
																<h4 class="modal-title"><?php echo $personel->name; ?> </h4>
																<button type="button" class="close" data-dismiss="modal" aria-label="Close">
																	<span aria-hidden="true">&times;</span>
																</button>
															</div>
															<div class="modal-body">
																<p>Silmek istediğinizden emin misiniz?&hellip;</p>
															</div>
															<div class="modal-footer justify-content-between">
																<button type="button" class="btn btn-outline-danger" data-dismiss="modal">Hayır</button>
																<a href="<?php echo base_url('Admin/personelsil/'.$personel->id.''); ?>" class="button"><button type="button" class="btn btn-outline-danger">Evet</button></a>
															</div>
														</div>
													</div>
												</div>
											</td>
										</tr>
									<?php } ?>
								<?php } ?>
							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
</section>
<?php $this->load->view('admin/include/footer'); ?>